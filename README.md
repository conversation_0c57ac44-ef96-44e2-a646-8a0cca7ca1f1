# 🏗️ Kansai Port Application

A comprehensive web application for Kansai Electric with advanced **issue reporting**, **data management dashboard**, and **PDF requirements extraction** capabilities. Features AI-powered document analysis, cloud storage integration, and a professional-grade dashboard with Japanese language support.

## 📋 Table of Contents
- [Features](#-features)
- [Prerequisites](#-prerequisites)
- [Quick Start Guide](#-quick-start-guide)
- [Environment Configuration](#-environment-configuration)
- [Running the Application](#-running-the-application)
- [Usage Guide](#-usage-guide)
- [API Documentation](#-api-documentation)
- [Development](#-development)
- [Troubleshooting](#-troubleshooting)
- [Support](#-support)

## 🌟 Features

### 1. Issue Report System
- **Multi-language Support**: Seamless English and Japanese interface with elegant typography
- **Automated Excel Generation**: Creates structured issue reports in Excel format with timestamps
- **Box.com Integration**: Automatic file upload to Box cloud storage with shared links
- **Database Storage**: PostgreSQL database for secure storage and retrieval of issue reports
- **Form Validation**: Advanced client-side and server-side validation with real-time feedback
- **Responsive Design**: Beautiful, professional interface that works perfectly on all devices

### 2. Enhanced Data Management Dashboard 🆕
- **Professional UI**: Production-ready dashboard with elegant design and smooth animations
- **Advanced Search**: Real-time search across all data fields (IDs, queries, Box links)
- **Smart Filtering**: Multiple filter criteria with instant results and search highlighting
- **Data Operations**: 
  - **Bulk Actions**: Multi-select records for batch operations with secure authentication
  - **Export Functionality**: CSV export for all data or selected records with Excel compatibility
  - **Copy Links**: One-click clipboard functionality for Box links
- **Column Sorting**: Click any header to sort data (ascending/descending)
- **Flexible Pagination**: Configurable items per page (10/25/50/100 records)
- **Statistics Overview**: Real-time dashboard with data insights and record counts
- **Security Features**:
  - **Multi-password Authentication**: Configure multiple access passwords for different user levels
  - **Double Confirmation**: Two-step verification for edit/delete operations
  - **Session Management**: Configurable timeouts and security settings
- **Auto-refresh**: Configurable automatic data updates (default: 30 seconds)
- **Toast Notifications**: Real-time feedback for all operations with elegant animations
- **Japanese Language Support**: Complete translation including complex dashboard elements

### 3. PDF Requirements Extractor
- **AI-Powered Extraction**: Uses IBM Watson X for intelligent document analysis
- **Multi-Method Table Detection**: 
  - Camelot for complex table structures
  - Tabula for PDF table parsing  
  - PDFPlumber for text-based extraction
- **LLM Fallback**: Advanced language model analysis for complex documents
- **Japanese Document Support**: Specialized for Japanese construction specification documents (要求事項)
- **Confidence Scoring**: Reliability assessment for extracted data with visual indicators
- **JSON Export**: Structured data output for further processing and integration

## 🔧 Prerequisites

Before you begin, ensure you have the following installed:

### Required Software
- **Python 3.8+** (recommended: Python 3.9 or 3.10)
- **Node.js 16+** (recommended: Node.js 18 LTS)
- **npm** (comes with Node.js)
- **PostgreSQL 12+** (for database functionality)
- **Git** (for cloning the repository)

### System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: At least 2GB free space
- **Internet Connection**: Required for API services and package installations

### Account Requirements
- **IBM Cloud Account**: For Watson X AI services
- **Box.com Account**: For cloud storage integration
- **PostgreSQL Database**: Local or cloud-hosted

## 🚀 Quick Start Guide

### Step 1: Clone the Repository
```bash
git clone <your-repository-url>
cd Kansai-port
```

### Step 2: Set Up Python Virtual Environment
```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### Step 3: Install Backend Dependencies
```bash
cd backend
pip install -r requirements.txt
cd ..
```

### Step 4: Install Frontend Dependencies
```bash
cd frontend

# If you encounter dependency conflicts, use this command:
npm install --legacy-peer-deps

# Otherwise, try the standard install first:
# npm install

cd ..
```

> **Note**: If you get a TypeScript dependency conflict error (ERESOLVE), use `npm install --legacy-peer-deps`. This resolves conflicts between TypeScript 4.x and newer i18next packages.

### Step 5: Configure Environment Variables

**⚠️ IMPORTANT**: You need to create ONE `.env` file for the backend. Frontend configuration is handled automatically.

#### Backend Environment File
```bash
# Create backend environment file
touch backend/.env

# Edit the .env file with your actual credentials (see detailed configuration below)
# Example: nano backend/.env or code backend/.env
```

#### Frontend Setup (No .env Required)
```bash
# No frontend .env file needed! 
# Configuration is handled automatically by the application
# Just install dependencies and start
```

> **📝 Note**: Only the backend `.env` file is required. Frontend configuration is handled automatically through the Settings UI and backend API.

### Step 6: Set Up Database
```bash
# Make sure PostgreSQL is running
# Create a database named 'querydb' (or update DB_NAME in .env)
createdb querydb

# The application will automatically create required tables
```

### Step 7: Start the Application
```bash
# Start backend (in one terminal)
cd backend
source venv/bin/activate  # if not already activated
python app.py

# Start frontend (in another terminal)
cd frontend
npm start
```

### Step 8: Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/docs

## ⚙️ Environment Configuration

**⚠️ CRITICAL SETUP**: This application requires ONE `.env` file for the backend. Frontend configuration is automatic.

### 1. Backend Environment Variables (`backend/.env`)

Create a `.env` file in the `backend` directory with the following variables:

```env
# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database connection settings
DB_NAME=querydb
DB_USER=postgres
DB_PASSWORD=your_postgres_password_here
DB_HOST=localhost
DB_PORT=5432

# =============================================================================
# BOX.COM CLOUD STORAGE CONFIGURATION
# =============================================================================
# Box application credentials (get from Box Developer Console)
# 1. Go to https://app.box.com/developers/console
# 2. Create a new app with "Custom App" using "Server Authentication (with JWT)" or "Standard OAuth 2.0"
# 3. Get your Client ID and Client Secret from the app configuration
BOX_CLIENT_ID=your_box_client_id_here
BOX_CLIENT_SECRET=your_box_client_secret_here

# Box OAuth tokens for API access
# To get these tokens:
# Method 1: Use Box Developer Console "Generate Developer Token" (temporary, expires in 1 hour)
# Method 2: Complete OAuth flow programmatically (permanent until revoked)
# Replace these placeholder values with your actual tokens:
ACCESS_TOKEN=your_box_access_token_here
REFRESH_TOKEN=your_box_refresh_token_here

# =============================================================================
# IBM WATSON X AI CONFIGURATION
# =============================================================================
# IBM Cloud Watson X AI credentials for PDF extraction
# 1. Go to https://cloud.ibm.com/
# 2. Create Watson X AI service instance
# 3. Get API key from service credentials
# 4. Create a project and get project ID
WATSONX_API_KEY=your_watsonx_api_key_here
WATSONX_PROJECT_ID=your_watsonx_project_id_here
IBM_CLOUD_URL=https://us-south.ml.cloud.ibm.com

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# Local folder for temporary file uploads (will be created automatically)
UPLOAD_FOLDER=./UploadedData
```

### How to Get Required Credentials

#### 1. PostgreSQL Database Setup
```bash
# Install PostgreSQL (if not already installed)
# On macOS:
brew install postgresql
brew services start postgresql

# On Ubuntu/Debian:
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql

# On Windows: Download from https://www.postgresql.org/download/windows/

# Create database and user
sudo -u postgres psql
CREATE DATABASE querydb;
CREATE USER your_username WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE querydb TO your_username;
\q
```

#### 2. Box.com Credentials
1. **Create Box Developer Account**:
   - Go to https://app.box.com/developers/console
   - Click "Create New App"
   - Choose "Custom App"
   - Select "Standard OAuth 2.0 (User Authentication)"
   - Fill in app details

2. **Get Client Credentials**:
   - In your app configuration, note down:
     - `Client ID` → Use for `BOX_CLIENT_ID`
     - `Client Secret` → Use for `BOX_CLIENT_SECRET`

3. **Get Access Tokens**:
   - **Quick Method (Temporary)**: In app configuration, click "Generate Developer Token"
   - **Permanent Method**: Complete OAuth flow (tokens last longer)

#### 3. IBM Watson X AI Credentials
1. **Create IBM Cloud Account**:
   - Go to https://cloud.ibm.com/
   - Sign up or log in

2. **Create Watson X AI Service**:
   - Search for "Watson X" in catalog
   - Create a service instance
   - Go to service credentials and create new credentials
   - Note down the `apikey`

3. **Create Project**:
   - Go to Watson X AI interface
   - Create a new project
   - Note down the `project_id` from project settings

### 2. Frontend Configuration (No .env Required)

**✅ SIMPLIFIED**: The frontend no longer requires a `.env` file! Configuration is now handled automatically through:

1. **Backend API Endpoint**: Frontend gets configuration from `/api/config`
2. **Settings Interface**: Users can configure URLs through the Settings tab in the application
3. **Local Storage**: Settings are automatically saved and persist across sessions
4. **Automatic Fallbacks**: System uses intelligent defaults if configuration is unavailable

#### Configuration Priority (Automatic):
1. **Settings UI** (highest priority) - User-configured settings in the application
2. **Backend `/api/config`** - Dynamic configuration from backend
3. **Environment Defaults** - Automatic fallback to `window.location.origin`

#### Manual Configuration (Optional):
If you need to override the default configuration, you can:
- Use the **Settings tab** in the application interface
- Set backend environment variable `FORM_SUBMIT_URL` for webmethods integration
- Set backend environment variable `DASHBOARD_API_URL` for custom API endpoints

## 🎯 Running the Application

### Method 1: Manual Start (Recommended for Development)

#### Start Backend:
```bash
cd backend

# Activate virtual environment (if not already active)
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Start the FastAPI server
python app.py

# You should see:
# INFO:     Uvicorn running on http://0.0.0.0:8080
```

#### Start Frontend:
```bash
# In a new terminal window
cd frontend

# Start React development server
npm start

# Browser should automatically open to http://localhost:3000
```

### Method 2: Production Start

#### Backend (Production):
```bash
cd backend
source venv/bin/activate
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app --bind 0.0.0.0:8080
```

#### Frontend (Production):
```bash
cd frontend
npm run build
# Serve the build folder with a web server like nginx or serve
npx serve -s build -l 3000
```

### Verify Installation

1. **Check Backend Health**:
   ```bash
   curl http://localhost:8080/health
   # Should return: {"status": "healthy", "timestamp": "..."}
   ```

2. **Check Frontend**:
   - Navigate to http://localhost:3000
   - You should see the Kansai Port application interface

3. **Check Database Connection**:
   - The backend logs should show successful database connection
   - Look for: "Database connection established successfully"

## 📱 Usage Guide

### Issue Reporting Feature

1. **Access the Issue Report Form**:
   - Navigate to http://localhost:3000
   - Click on "Issue Report" tab (問題報告 in Japanese)

2. **Fill Out the Form**:
   - **Date**: Select the date when the issue occurred
   - **Issue Title**: Brief summary of the issue
   - **Description**: Detailed description of the problem
   - **Language Support**: Interface automatically adapts to selected language

3. **Submit the Report**:
   - Click "Submit Report" (送信 in Japanese)
   - The system will:
     - Validate the form data with real-time feedback
     - Create an Excel file with the issue details and timestamp
     - Upload the file to Box.com cloud storage
     - Store the report in PostgreSQL database
     - Return a confirmation with Box shared link

4. **Access Your Reports**:
   - Reports are automatically uploaded to your Box.com account
   - Database stores metadata for tracking and retrieval
   - Use the Dashboard to view and manage all reports

### Enhanced Dashboard Feature 🆕

1. **Access the Dashboard**:
   - Navigate to http://localhost:3000
   - Click on "Dashboard" tab (ダッシュボード in Japanese)

2. **View and Search Data**:
   - **Real-time Search**: Use the search bar to find specific records by ID, content, or Box links
   - **Sort Data**: Click column headers to sort by ID, queries, or Box links
   - **Filter Results**: Use advanced filtering options for precise data discovery
   - **Statistics**: View real-time overview of total records, links status, and last update time

3. **Data Management Operations**:
   - **Edit Records**: 
     - Click "Edit" (編集) button on any record
     - Modify queries or Box link in the dialog
     - Enter password to confirm changes
   - **Delete Records**:
     - Single deletion: Click "Delete" (削除) button
     - Bulk deletion: Select multiple records using checkboxes
     - Password authentication required for all deletions
   - **Export Data**:
     - Click "Export to CSV" (CSVにエクスポート) for all data
     - Select records and use "Export Selected" (選択項目をエクスポート) for subset
     - Files download automatically with timestamps

4. **Advanced Features**:
   - **Auto-refresh**: Data updates automatically every 30 seconds
   - **Manual Refresh**: Use refresh button for immediate updates
   - **Copy Links**: One-click copy Box links to clipboard
   - **Pagination**: Configure items per page (10/25/50/100 records)
   - **Toast Notifications**: Real-time feedback for all operations

5. **Security and Authentication**:
   - **Multi-password Support**: Configure multiple access passwords in `backend/config.yaml`
   - **Double Confirmation**: Two-step verification for destructive operations
   - **Session Management**: Configurable session timeouts

### PDF Requirements Extraction Feature

1. **Access the PDF Extractor**:
   - Navigate to http://localhost:3000
   - Click on "PDF Extractor" tab

2. **Upload a PDF**:
   - Click "Choose File" or drag-and-drop a PDF
   - Supported: Construction specification documents (Japanese text supported)
   - Maximum file size: 50MB

3. **Processing**:
   - The system uses multiple extraction methods:
     1. **Table Detection**: Camelot, Tabula, PDFPlumber
     2. **AI Analysis**: IBM Watson X for complex content
     3. **Confidence Scoring**: Reliability assessment

4. **Review Results**:
   - Extracted requirements are displayed with confidence scores
   - Green: High confidence (>80%)
   - Yellow: Medium confidence (50-80%)
   - Red: Low confidence (<50%)

5. **Export Data**:
   - Download results as JSON for further processing
   - Use in other systems or applications

## 📊 API Documentation

### Core Endpoints

#### Health Check
```bash
GET /health
# Response: {"status": "healthy", "timestamp": "2024-01-15T10:30:00Z"}
```

#### Submit Issue Report
```bash
POST /api/issues
Content-Type: application/json

{
  "date": "2024-01-15",
  "issue": "Equipment malfunction in Unit 2",
  "description": "Detailed description of the issue including symptoms and impact"
}

# Response:
{
  "message": "Issue report submitted successfully",
  "issue_id": 123,
  "box_link": "https://app.box.com/s/xyz123",
  "file_name": "issue_report_123_2024-01-15.xlsx"
}
```

#### Extract PDF Requirements
```bash
POST /api/extract
Content-Type: multipart/form-data

# Form data:
file: [PDF file]

# Response:
{
  "status": "success",
  "extracted_data": {
    "requirements": [
      {
        "content": "Requirement text",
        "confidence": 0.95,
        "source": "table_detection"
      }
    ],
    "metadata": {
      "extraction_time": "2024-01-15T10:30:00Z",
      "file_name": "document.pdf",
      "methods_used": ["camelot", "watsonx"]
    }
  }
}
```

#### Legacy Upload Endpoint
```bash
POST /upload
Content-Type: multipart/form-data

# Form data:
file: [PDF file]

# Response: Similar to /api/extract
```

### Error Responses

All endpoints return consistent error formats:
```json
{
  "error": "Error description",
  "detail": "Detailed error information",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (invalid input)
- `500`: Internal Server Error
- `422`: Validation Error

## 🛠️ Development

### Backend Development

#### Project Structure
```
backend/
├── src/                    # Core application modules
│   ├── box_service.py     # Box.com integration
│   ├── db_service.py      # Database operations
│   ├── pipeline.py        # PDF extraction pipeline
│   ├── watsonx_client.py  # IBM Watson X integration
│   ├── config.py          # Configuration management
│   └── models.py          # Data models
├── utils/                 # Utility functions
│   └── file_utils.py      # File operations
├── app.py                 # Main FastAPI application
├── requirements.txt       # Python dependencies
└── .env                   # Environment variables
```

#### Adding New Dependencies
```bash
cd backend
source venv/bin/activate

# Install new package
pip install new-package

# Update requirements file
pip freeze > requirements.txt
```

#### Database Management
```bash
# Connect to database
psql -h localhost -U postgres -d querydb

# View tables
\dt

# View specific table
SELECT * FROM issues LIMIT 10;
```

#### Running with Debug Mode
```bash
cd backend
source venv/bin/activate

# Run with auto-reload for development
python -c "import uvicorn; uvicorn.run('app:app', host='0.0.0.0', port=8080, reload=True)"
```

### Frontend Development

#### Project Structure
```
frontend/
├── src/
│   ├── components/        # React components
│   │   ├── IssueForm.tsx  # Issue reporting form
│   │   ├── PDFExtractor.tsx # PDF extraction interface
│   │   └── LanguageSelector.tsx # Language switcher
│   ├── i18n.ts           # Internationalization config
│   ├── App.tsx           # Main application component
│   └── index.tsx         # Application entry point
├── public/               # Static assets
├── package.json          # Node.js dependencies
└── tsconfig.json         # TypeScript configuration
```

#### Development Server
```bash
cd frontend

# Start development server with hot reload
npm start

# The app will open at http://localhost:3000
# Changes will automatically reload the page
```

#### Building for Production
```bash
cd frontend

# Create production build
npm run build

# Test production build locally
npx serve -s build -l 3000
```

#### Adding New Dependencies
```bash
cd frontend

# Install new package (use --legacy-peer-deps if conflicts occur)
npm install package-name --legacy-peer-deps

# Install TypeScript types (if needed)
npm install @types/package-name --save-dev --legacy-peer-deps
```

#### Making Legacy Peer Deps Permanent (Optional)
If you frequently encounter dependency conflicts, create an `.npmrc` file:
```bash
cd frontend
echo "legacy-peer-deps=true" > .npmrc
# This makes all npm installs use legacy peer dependency resolution
```

### Code Quality

#### Backend Linting and Formatting
```bash
cd backend

# Install development tools
pip install black flake8 isort

# Format code
black .

# Check code style
flake8 .

# Sort imports
isort .
```

#### Frontend Linting and Formatting
```bash
cd frontend

# Install ESLint and Prettier
npm install --save-dev eslint prettier

# Lint code
npm run lint

# Format code
npm run format
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Backend Won't Start

**Error**: `ModuleNotFoundError: No module named 'xyz'`
```bash
# Solution: Install missing dependencies
cd backend
source venv/bin/activate
pip install -r requirements.txt
```

**Error**: `Database connection failed`
```bash
# Solution: Check PostgreSQL is running and credentials are correct
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# Check database exists
psql -h localhost -U postgres -l | grep querydb
```

**Error**: `Port 8080 already in use`
```bash
# Solution: Kill process using the port
lsof -ti:8080 | xargs kill -9

# Or use different port
python app.py --port 8081
```

#### 2. Frontend Won't Start

**Error**: `npm: command not found`
```bash
# Solution: Install Node.js
# Download from https://nodejs.org/
# Or use package manager:
brew install node  # macOS
sudo apt install nodejs npm  # Ubuntu
```

**Error**: `npm install` fails with ERESOLVE dependency conflicts
```bash
# Error message like: "ERESOLVE could not resolve" with TypeScript conflicts
# Solution: Use legacy peer deps flag
cd frontend
npm install --legacy-peer-deps

# Alternative: Clear cache and try again
npm cache clean --force
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

**Error**: `Frontend can't connect to backend` or `Network Error`
```bash
# Solution: Check backend connectivity and configuration
# 1. Verify backend is running on the expected URL
# 2. Check browser console for CORS errors
# 3. Use Settings tab in the application to configure URLs

# Check if backend is accessible:
curl http://localhost:8080/health

# If backend runs on different port, either:
# Option 1: Use Settings tab in the application UI
# Option 2: Set backend environment variable:
echo "DASHBOARD_API_URL=http://localhost:8081" >> backend/.env
```

**Error**: `Module not found` or other dependency issues
```bash
# Solution: Clear and reinstall dependencies
cd frontend
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

**Error**: `Port 3000 already in use`
```bash
# Solution: Kill process or use different port
lsof -ti:3000 | xargs kill -9
# Or start with different port
PORT=3001 npm start
```

#### 3. Box.com Integration Issues

**Error**: `Refresh token has expired`
```bash
# Solution: Get new tokens from Box Developer Console
# 1. Go to https://app.box.com/developers/console
# 2. Select your app
# 3. Click "Generate Developer Token"
# 4. Update ACCESS_TOKEN and REFRESH_TOKEN in .env
```

**Error**: `Invalid client credentials`
```bash
# Solution: Verify Box app configuration
# 1. Check BOX_CLIENT_ID and BOX_CLIENT_SECRET in .env
# 2. Ensure app is approved in Box Developer Console
# 3. Check OAuth redirect URI if using OAuth flow
```

#### 4. PDF Extraction Issues

**Error**: `Watson X API authentication failed`
```bash
# Solution: Verify IBM Cloud credentials
# 1. Check WATSONX_API_KEY in .env
# 2. Verify project exists and WATSONX_PROJECT_ID is correct
# 3. Test API access:
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://us-south.ml.cloud.ibm.com/ml/v1/projects/YOUR_PROJECT_ID"
```

**Error**: `PDF extraction timeout`
```bash
# Solution: Check file size and complexity
# 1. Ensure PDF is less than 50MB
# 2. Try with simpler PDF first
# 3. Check internet connection for Watson X API calls
```

**Error**: `No tables detected in PDF`
```bash
# Solution: Verify PDF format
# 1. Ensure PDF contains actual tables (not images)
# 2. Try OCR if PDF is scanned image
# 3. Check if text is selectable in PDF viewer
```

#### 5. CORS Issues

**Error**: `Access to XMLHttpRequest blocked by CORS policy`
```bash
# Solution: Check CORS configuration
# 1. Ensure backend allows frontend origin (check app.py CORS middleware)
# 2. Verify backend is running on expected port
# 3. Use Settings tab in application to configure URLs if needed

# Check backend CORS configuration in app.py:
# Should include: allow_origins=["http://localhost:3000", ...]

# Restart both frontend and backend after configuration changes
```

#### 6. Database Issues

**Error**: `relation "issues" does not exist`
```bash
# Solution: Create database tables
# The application should auto-create tables on first run
# If not, check database permissions and connection
```

**Error**: `Authentication failed for user`
```bash
# Solution: Check PostgreSQL user permissions
sudo -u postgres psql
ALTER USER your_username WITH PASSWORD 'new_password';
GRANT ALL PRIVILEGES ON DATABASE querydb TO your_username;
```

### Performance Issues

#### Slow PDF Processing
1. **Check Watson X quota**: Verify you haven't exceeded API limits
2. **Reduce file size**: Compress PDF or split large documents
3. **Optimize extraction**: Disable unnecessary extraction methods in config

#### High Memory Usage
1. **Backend**: Increase server memory or use smaller batch sizes
2. **Frontend**: Clear browser cache and disable unnecessary browser extensions

### Logging and Debugging

#### Enable Debug Mode
```bash
# Backend debug logging
export LOG_LEVEL=DEBUG
python app.py

# Frontend debug mode
export REACT_APP_DEBUG=true
npm start
```

#### View Logs
```bash
# Backend logs
tail -f backend/app.log

# Frontend logs
# Open browser developer console (F12)
# Check Console tab for errors
```

#### API Testing
```bash
# Test backend health
curl http://localhost:8080/health

# Test with verbose output
curl -v -X POST http://localhost:8080/api/issues \
  -H "Content-Type: application/json" \
  -d '{"date":"2024-01-15","issue":"test","description":"test"}'
```

### Environment Variables Checklist

Before seeking help, verify your environment configuration:

#### Backend Checklist (`backend/.env`):
- [ ] Database credentials (DB_NAME, DB_USER, DB_PASSWORD, etc.)
- [ ] Box.com credentials (BOX_CLIENT_ID, BOX_CLIENT_SECRET, etc.)
- [ ] Watson X credentials (WATSONX_API_KEY, WATSONX_PROJECT_ID)
- [ ] UPLOAD_FOLDER path set

#### Frontend Checklist (No .env Required):
- [ ] **Settings configured** via the Settings tab in the application UI
- [ ] **Backend accessible** at the expected URL
- [ ] **Browser console** checked for any configuration errors

#### Quick Verification Commands:
```bash
# Check backend .env file exists
ls -la backend/.env

# Verify backend is accessible
curl http://localhost:8000/health

# Check backend logs for errors
tail -f backend/logs/app.log

# Test frontend configuration endpoint
curl http://localhost:8000/api/config
```

### Getting Help

1. **Check API Documentation**: http://localhost:8000/docs
2. **Review Logs**: Check both backend logs and browser console
3. **Test Components Individually**: Use curl for API testing
4. **Check Network Connectivity**: Ensure all external services are accessible
5. **Verify Environment Files**: Both `.env` files must be properly configured

## 📞 Support

### Contact Information
- **Technical Issues**: Submit issues in the project repository
- **API Documentation**: Available at `/docs` when backend is running
- **Configuration Help**: Review this README's configuration section

### Useful Resources
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://reactjs.org/docs/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Box API Documentation](https://developer.box.com/reference/)
- [IBM Watson X Documentation](https://www.ibm.com/docs/en/watsonx-as-a-service)

### Contributing
1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and test thoroughly
4. Submit a pull request with detailed description

---

## 📋 Quick Setup Summary

For experienced developers, here's the essential setup:

1. **Prerequisites**: Python 3.8+, Node.js 16+, PostgreSQL 12+
2. **Clone & Install**:
   ```bash
   git clone <repo-url> && cd Kansai-port
   python3 -m venv venv && source venv/bin/activate
   cd backend && pip install -r requirements.txt && cd ../frontend
   npm install --legacy-peer-deps && cd ..
   ```
3. **Environment File** (CRITICAL):
   ```bash
   # Create backend/.env with database, Box, and Watsonx credentials
   # Frontend configuration is automatic - no .env needed!
   ```
4. **Database**: `createdb querydb` (or update DB_NAME in backend/.env)
5. **Run**: 
   ```bash
   # Terminal 1: cd backend && python app.py
   # Terminal 2: cd frontend && npm start
   ```
6. **Access**: http://localhost:3000

**⚠️ Remember**: Only the backend `.env` file is required - frontend configuration is automatic!
