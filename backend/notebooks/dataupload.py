import os
import json
import psycopg2
import pandas as pd
from boxsdk import OAuth2, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Step 0: Setup
query_text = "machine 4 went down"

# ---------- Step 1: Insert query into PostgreSQL ----------
def get_connection():
    return psycopg2.connect(
        dbname=os.getenv("DB_NAME"),
        user=os.getenv("DB_USER"),
        password=os.getenv("DB_PASSWORD"),
        host=os.getenv("DB_HOST", "localhost"),
        port=os.getenv("DB_PORT", "5432")
    )

def insert_query_get_id(conn, query):
    cur = conn.cursor()
    cur.execute("INSERT INTO query_links (queries, box_link) VALUES (%s, %s) RETURNING id", (query, None))
    inserted_id = cur.fetchone()[0]
    conn.commit()
    cur.close()
    return inserted_id

# ---------- Step 2: Create Excel file ----------
def create_excel(query, file_path):
    df = pd.DataFrame({"Query": [query]})
    df.to_excel(file_path, index=False)

# ---------- Step 3: Upload to Box and get link ----------
def store_tokens_callback(new_access_token, new_refresh_token):
    with open(os.getenv("BOX_TOKENS_FILE"), "w") as f:
        json.dump({
            "access_token": new_access_token,
            "refresh_token": new_refresh_token
        }, f)

def upload_to_box(file_path, file_name, folder_id="0"):
    with open(os.getenv("BOX_TOKENS_FILE")) as f:
        tokens = json.load(f)

    oauth = OAuth2(
        client_id=os.getenv("BOX_CLIENT_ID"),
        client_secret=os.getenv("BOX_CLIENT_SECRET"),
        access_token=tokens['access_token'],
        refresh_token=tokens['refresh_token'],
        store_tokens=store_tokens_callback
    )

    client = Client(oauth)
    uploaded_file = client.folder(folder_id).upload(file_path, file_name)
    shared_link = uploaded_file.get_shared_link()
    print(f"✅ Uploaded: {uploaded_file.name}")
    print(f"🔗 Link: {shared_link}")
    return shared_link

# ---------- Step 4: Update link in PostgreSQL ----------
def update_box_link(conn, row_id, box_link):
    cur = conn.cursor()
    cur.execute("UPDATE query_links SET box_link = %s WHERE id = %s", (box_link, row_id))
    conn.commit()
    cur.close()

# ---------- MAIN ----------
def main():
    conn = get_connection()

    try:
        # Step 1
        row_id = insert_query_get_id(conn, query_text)

        # Step 2
        file_name = f"query_{row_id}.xlsx"
        file_path = os.path.join(os.getenv("UPLOAD_FOLDER"), file_name)
        create_excel(query_text, file_path)

        # Step 3
        box_link = upload_to_box(file_path, file_name)

        # Step 4
        update_box_link(conn, row_id, box_link)

        print("Done! Query inserted, Excel uploaded, DB updated.")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
