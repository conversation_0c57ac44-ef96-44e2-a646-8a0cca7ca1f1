{"cells": [{"cell_type": "code", "execution_count": null, "id": "ed41d4a5", "metadata": {}, "outputs": [], "source": ["# import os\n", "# import base64\n", "from PyPDF2 import PdfReader\n", "# import requests\n", "# import json\n", "from dotenv import load_dotenv\n", "from ibm_watson_machine_learning.foundation_models import Model\n", "from ibm_watson_machine_learning.metanames import GenTextParamsMetaNames as GenParams\n", "import os\n", "import json"]}, {"cell_type": "code", "execution_count": 3, "id": "d2f9a165", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Credentials loaded successfully.\n"]}], "source": ["# load_dotenv()\n", "load_dotenv(override=True)\n", "api_key = os.getenv(\"WATSONX_API_KEY\", None)\n", "ibm_cloud_url = os.getenv(\"IBM_CLOUD_URL\", None)\n", "project_id = os.getenv(\"WATSONX_PROJECT_ID\", None)\n", "\n", "if api_key is None or ibm_cloud_url is None or project_id is None:\n", "    raise Exception(\"Missing API Key, IBM Cloud URL, or Project ID.\")\n", "\n", "# Set up Watsonx.ai credentials\n", "creds = {\n", "    \"url\": ibm_cloud_url,\n", "    \"apikey\": api_key\n", "}\n", "print(\"Credentials loaded successfully.\")"]}, {"cell_type": "code", "execution_count": 4, "id": "ac6e89de", "metadata": {}, "outputs": [], "source": ["def send_to_watsonxai(prompts,\n", "                      model_name=\"meta-llama/llama-3-3-70b-instruct\",\n", "                      decoding_method=\"greedy\",\n", "                      max_new_tokens=8192,\n", "                      min_new_tokens=1,\n", "                      temperature=0.3,\n", "                      repetition_penalty=1.2,\n", "                    ):\n", "    \"\"\"\n", "    Helper function to send prompts and parameters to Watsonx.ai LLM.\n", "\n", "    Args:\n", "        prompts (list): List of text prompts.\n", "        decoding_method (str): \"sample\" or \"greedy\".\n", "        max_new_tokens (int): Max tokens to generate.\n", "        min_new_tokens (int): Minimum new tokens.\n", "        temperature (float): LLM temperature for creativity.\n", "        repetition_penalty (float): Penalty for repetition.\n", "\n", "    Returns:\n", "        str: Generated response.\n", "    \"\"\"\n", "\n", "    assert not any(map(lambda prompt: len(prompt) < 1, prompts)), \"Empty prompt detected!\"\n", "\n", "    # Define model parameters\n", "    model_params = {\n", "        GenParams.DECODING_METHOD: decoding_method,\n", "        GenParams.MIN_NEW_TOKENS: min_new_tokens,\n", "        GenParams.MAX_NEW_TOKENS: max_new_tokens,\n", "        GenParams.RANDOM_SEED: 42,\n", "        GenParams.TEMPERATURE: temperature,\n", "        GenParams.REPETITION_PENALTY: repetition_penalty,\n", "    }\n", "\n", "    # Instantiate LLM Model\n", "    model = Model(\n", "        model_id=model_name,\n", "        params=model_params,\n", "        credentials=creds,\n", "        project_id=project_id\n", "    )\n", "\n", "    # Call the model with prompt\n", "    for prompt in prompts:\n", "        return model.generate_text(prompt)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "60987ba5", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def extract_json_string(text):\n", "    # Remove markdown blocks\n", "    text = text.replace(\"```json\", \"\").replace(\"```\", \"\")\n", "    \n", "    # Try to extract the first JSON array\n", "    match = re.search(r'\\[\\s*{.*?}\\s*]', text, re.DOTALL)\n", "    return match.group(0) if match else None"]}, {"cell_type": "code", "execution_count": 6, "id": "144c1db7", "metadata": {}, "outputs": [], "source": ["def clean_and_fix_json(json_string):\n", "    # Remove any accidental lone key-value breaks like: \"\", or stray commas\n", "    json_string = re.sub(r',\\s*\"\"\\s*,', ',', json_string)       # middle\n", "    json_string = re.sub(r'\"\",\\s*', '', json_string)            # leading\n", "    json_string = re.sub(r',\\s*\"\"\\s*:', '', json_string)        # trailing\n", "\n", "    # Remove broken dict entries like: '\"\",'\n", "    json_string = re.sub(r'{\\s*\"index\":\\s*\"[^\"]*\",\\s*\"\",', r'{ \"index\":', json_string)\n", "\n", "    # Optional: Remove BOM if any\n", "    json_string = json_string.replace('\\ufeff', '')\n", "\n", "    return json_string\n"]}, {"cell_type": "code", "execution_count": 7, "id": "c92d6f6f", "metadata": {}, "outputs": [], "source": ["def fix_common_json_bugs(broken_json_str):\n", "    # Fix broken {\"index\": \"requirement_item_ja\": ...} case\n", "    broken_json_str = re.sub(\n", "        r'\"index\":\\s*\"requirement_item_ja\"\\s*:',\n", "        '\"index\": \"(1)\",\\n  \"requirement_item_ja\":',\n", "        broken_json_str\n", "    )\n", "\n", "    # Remove BOM\n", "    broken_json_str = broken_json_str.replace('\\ufeff', '')\n", "\n", "    # (Optional) Replace full-width dash with standard one\n", "    broken_json_str = broken_json_str.replace('\\u30fc', '-')\n", "\n", "    return broken_json_str\n"]}, {"cell_type": "code", "execution_count": null, "id": "7f8f76e6", "metadata": {}, "outputs": [], "source": ["def extract_tables_from_pdf(pdf_path):\n", "    \"\"\"Extract specific tables from a Japanese PDF using LLaMA 4 Maverick.\"\"\"\n", "    \n", "    # Read PDF text\n", "    with open(pdf_path, \"rb\") as file:\n", "        pdf_reader = PdfReader(file)\n", "        text_content = \"\"\n", "        for page in pdf_reader.pages:\n", "            content = page.extract_text()\n", "            if content:\n", "                text_content += content + \"\\n\"\n", "\n", "    # Truncate to fit within token limits (adjust if needed)\n", "    text_chunk = text_content[:8000]\n", "\n", "    # Prompt for Japanese table extraction\n", "    prompt = f\"\"\"\n", "    You are an expert document parser. Extract the following table from a Japanese document into structured JSON format. The table consists of three columns:\n", "\n", "    1. An index like (1), (2), etc.\n", "    2. A requirement description in Japanese (要  求  事  項)\n", "    3. A symbol (〇) if a \"Site Meeting\" (立会項目) is required, otherwise leave it blank.\n", "    4.Every dictionary in the JSON array must have all three keys: index, requirement_item_ja, and site_meeting_required.\n", "    If any value is missing, use an empty string \"\". Do not include any empty or partial entries like \"\" : \"\".\n", "\n", "    Preserve the Japanese text exactly.\n", "\n", "    Your output must be a JSON array in this format:\n", "\n", "    [\n", "      {{\n", "        \"index\": \"(1)\",\n", "        \"requirement_item_ja\": \"不法無線局を搭載した車両は使用しないこと。\",\n", "        \"site_meeting_required\": \"\"\n", "      }},\n", "      {{\n", "        \"index\": \"(3)\",\n", "        \"requirement_item_ja\": \"作業着手前に当社が行う電源開放操作、系統隔離操作にて確認のため立会うこと。\",\n", "        \"site_meeting_required\": \"〇\"\n", "      }}\n", "    ]\n", "\n", "    Only return the JSON array. Do not include any explanations.\n", "\n", "    PDF content (partial):\n", "    {text_chunk}\n", "  \"\"\"\n", "\n", "    # Call your LLM (LLaMA 4 Maverick) via WatsonX\n", "    response = send_to_watsonxai(\n", "        prompts=[prompt],\n", "        model_name=\"meta-llama/llama-4-maverick-17b-128e-instruct-fp8\",\n", "        decoding_method=\"greedy\",\n", "        max_new_tokens=2000,\n", "        min_new_tokens=1,\n", "        temperature=0.2,\n", "        repetition_penalty=1.2\n", "    )\n", "\n", "    json_string = extract_json_string(response)\n", "    json_string = clean_and_fix_json(json_string)\n", "    json_string = fix_common_json_bugs(json_string)\n", "\n", "    try:\n", "        parsed = json.loads(json_string)\n", "        return parsed\n", "    except Exception as e:\n", "        print(\"⚠️ JSON still invalid after cleaning.\")\n", "        print(json_string)\n", "        raise e"]}, {"cell_type": "code", "execution_count": 9, "id": "8d62908c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'index': '(1)', 'requirement_item_ja': '不法無線局を搭載した車両は使用しないこと。', 'site_meeting_required': ''}, {'index': '(2)', 'requirement_item_ja': '発電所構内におけるル-ル「堺港発電所\\u3000入構者心得」を遵守すること。', 'site_meeting_required': ''}, {'index': '(3)', 'requirement_item_ja': '作業着手前に当社が行う電源開放操作、系統隔離操作に確認のため立会うこと。', 'site_meeting_required': '〇'}, {'index': '(4)', 'requirement_item_ja': '撤去品（鉄くず等）は、材質別に仕分け、裁断および計量を行い、発電所構内の当社指定場所まで運搬し、整理すること。', 'site_meeting_required': ''}, {'index': '(5)', 'requirement_item_ja': '点検対象機器は、別紙１の通りとする。ただし、以下は対象外とする。\\n・建物関係照明設備：事務所、西側別館事務所、既設本館\\n・(堀港旧建物)自火報設備：務所', 'site_meeting_required': ''}, {'index': '(6)', 'requirement_item_ja': '点検内容は、別紙２、３の通りとする。', 'site_meeting_required': ''}, {'index': '(7)', 'requirement_item_ja': '報告書は、「消防用設備等点検結果報告書」の様式により当社に３部ずつ提出すること。', 'site_meeting_required': ''}, {'index': '(8)', 'requirement_item_ja': '点検できない場合、また仕様書と現場に差異がある場合は速やかに当社へ報告すること。', 'site_meeting_required': ''}, {'index': '(9)', 'requirement_item_ja': '社給材料および貸与物品一覧については、様式－７の通りとする。', 'site_meeting_required': ''}, {'index': '(10)', 'requirement_item_ja': '報告書受領時に点検数量が当社仕様書と合致しているために、報告書に点検チェックリストを設け、点検漏れがないようにすること。', 'site_meeting_required': ''}]\n"]}], "source": ["pdf_file = input(\"Enter the path to your PDF file: \")\n", "tables = extract_tables_from_pdf(pdf_file)\n", "print(tables)"]}, {"cell_type": "code", "execution_count": 10, "id": "b2ead116", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables saved to 【一般請求　工事】20250201_堺港発　自火報定修実施月調整工事(管理棟誘導灯他点検工事)_建物_tables.json\n"]}], "source": ["# Save to file with the PDF name\n", "output_filename = os.path.splitext(os.path.basename(pdf_file))[0] + \"_tables.json\"\n", "with open(output_filename, \"w\", encoding=\"utf-8\") as f:\n", "    json.dump(tables, f, ensure_ascii=False, indent=2)\n", "print(f\"Tables saved to {output_filename}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}