"""
LLM-based extraction functionality for requirements when table detection fails.
"""

import json
import logging
import re
from typing import Dict, List, Any, Optional

from services.watsonx_client import WatsonXLLM
from utils.models import Requirement
from utils.extraction_utils import create_summary_stats

logger = logging.getLogger(__name__)


class LLMExtractor:
    """Extracts requirements using LLM when table extraction fails"""
    
    def __init__(self, watsonx_llm: WatsonXLLM):
        """
        Initialize LLM extractor
        
        Args:
            watsonx_llm: WatsonX LLM client
        """
        self.watsonx_llm = watsonx_llm
        self.max_text_length = 8000  # Limit to avoid token limits
        logger.info("LLMExtractor initialized")
    
    def extract_requirements_with_llm(self, document_text: str) -> Dict[str, Any]:
        """
        Extract requirements using LLM
        
        Args:
            document_text: Full text of the document
            
        Returns:
            Extracted requirements data
        """
        try:
            logger.info("Starting LLM-based requirements extraction")
            
            # Truncate text if too long
            if len(document_text) > self.max_text_length:
                document_text = document_text[:self.max_text_length]
                logger.warning(f"Document text truncated to {self.max_text_length} characters")
            
            # Create prompt for requirement extraction
            prompt = self._create_extraction_prompt(document_text)
            
            # Call WatsonX LLM
            logger.debug("Sending prompt to WatsonX LLM")
            response = self.watsonx_llm.generate_with_retry(prompt)
            
            # Parse LLM response to structured data
            parsed_data = self._parse_llm_response(response)
            
            logger.info(f"LLM extraction completed. Found {parsed_data.get('total_count', 0)} requirements")
            return parsed_data
            
        except Exception as e:
            logger.error(f"LLM extraction failed: {str(e)}")
            return self._create_error_response(str(e))
    
    def _create_extraction_prompt(self, document_text: str) -> str:
        """Create a prompt for extracting requirements"""
        
        prompt = f"""あなたは日本の建設工事仕様書の専門家です。以下の文書から「要求事項」に関する情報を正確に抽出してください。

文書内容:
{document_text}

以下のJSON形式で要求事項を抽出してください：

{{
    "requirements": [
        {{
            "requirement": "要求事項の具体的な内容",
            "witness": "立会項目（○、×、または具体的な立会内容）",
            "item": "項目番号や分類（例：（１）、（２）、a.、b.など）",
            "details": "補足情報や詳細"
        }}
    ],
    "total_count": 抽出した要求事項の総数,
    "extraction_confidence": 抽出結果の信頼度（0.0-1.0の数値）
}}

抽出ルール：
1. 「要求事項」「立会項目」「工事範囲」「作業」「実施事項」などの見出しに注目
2. 表形式でない場合も、箇条書きや段落から具体的な要求事項を抽出
3. 「○○すること」「○○を行う」「○○に注意する」などの指示文を重視
4. 立会が必要な項目は「○」「×」または具体的な立会内容を記録
5. 項目番号（（１）（２）、a.b.など）がある場合は必ず含める
6. 空の要求事項は含めない
7. 重複する内容は除外する

重要：必ず有効なJSON形式で出力してください。

JSON:"""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response to extract structured data"""
        try:
            logger.debug("Parsing LLM response")
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                parsed_data = json.loads(json_str)
                
                # Validate and clean the parsed data
                cleaned_data = self._validate_and_clean_data(parsed_data)
                
                # Add metadata
                cleaned_data['extraction_method'] = 'llm'
                cleaned_data['raw_response'] = response[:500]  # Store truncated response
                
                return cleaned_data
            else:
                logger.warning("No JSON found in LLM response, using fallback parsing")
                return self._create_fallback_structure(response)
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON from LLM response: {str(e)}")
            return self._create_fallback_structure(response)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {str(e)}")
            return self._create_fallback_structure(response)
    
    def _validate_and_clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean parsed data from LLM"""
        try:
            cleaned_data = {
                'requirements': [],
                'total_count': 0,
                'extraction_confidence': 0.5
            }
            
            # Extract requirements
            requirements = data.get('requirements', [])
            if not isinstance(requirements, list):
                requirements = []
            
            valid_requirements = []
            for req in requirements:
                if isinstance(req, dict) and 'requirement' in req:
                    req_text = str(req['requirement']).strip()
                    if req_text and req_text.lower() not in ['nan', 'none', '']:
                        # Create Requirement object and convert to dict
                        requirement_obj = Requirement(
                            requirement=req_text,
                            witness=req.get('witness'),
                            item=req.get('item'),
                            details=req.get('details'),
                            source_page=None,  # Not available from LLM
                            extraction_method='llm',
                            confidence=data.get('extraction_confidence', 0.7)
                        )
                        valid_requirements.append(requirement_obj.to_dict())
            
            cleaned_data['requirements'] = valid_requirements
            cleaned_data['total_count'] = len(valid_requirements)
            
            # Extract confidence
            confidence = data.get('extraction_confidence', 0.7)
            if isinstance(confidence, (int, float)) and 0 <= confidence <= 1:
                cleaned_data['extraction_confidence'] = confidence
            
            # Create summary
            cleaned_data['summary'] = create_summary_stats(valid_requirements)
            
            logger.debug(f"Validated and cleaned {len(valid_requirements)} requirements")
            return cleaned_data
            
        except Exception as e:
            logger.error(f"Error validating/cleaning data: {str(e)}")
            return self._create_fallback_structure("Data validation failed")
    
    def _create_fallback_structure(self, response: str) -> Dict[str, Any]:
        """Create fallback structure when JSON parsing fails"""
        
        # Try to extract requirements from raw text
        requirements = self._extract_requirements_from_text(response)
        
        return {
            'requirements': requirements,
            'total_count': len(requirements),
            'extraction_confidence': 0.5,
            'extraction_method': 'llm_fallback',
            'raw_response': response[:500],
            'summary': create_summary_stats(requirements)
        }
    
    def _extract_requirements_from_text(self, text: str) -> List[Dict[str, Any]]:
        """Extract requirements from plain text using patterns"""
        try:
            requirements = []
            
            # Split text into lines
            lines = text.split('\n')
            
            # Patterns for requirement identification
            req_patterns = [
                r'.*すること[。．]*$',
                r'.*を行う[。．]*$', 
                r'.*を実施[。．]*$',
                r'.*を確認[。．]*$',
                r'.*を遵守[。．]*$',
                r'.*に注意[。．]*$'
            ]
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # Check if line matches requirement patterns
                is_requirement = any(re.search(pattern, line) for pattern in req_patterns)
                
                if is_requirement:
                    # Extract item number if present
                    item_match = re.search(r'[（(]\d+[）)]|[a-z]\.|[ａ-ｚ]．', line)
                    item = item_match.group() if item_match else None
                    
                    # Create requirement
                    requirement = {
                        'requirement': line,
                        'witness': None,
                        'item': item,
                        'details': None,
                        'source_page': None,
                        'extraction_method': 'llm_text_pattern',
                        'confidence': 0.4
                    }
                    requirements.append(requirement)
            
            logger.debug(f"Extracted {len(requirements)} requirements using text patterns")
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting from text: {str(e)}")
            return []
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create error response structure"""
        return {
            'requirements': [],
            'total_count': 0,
            'extraction_confidence': 0.0,
            'extraction_method': 'llm_error',
            'error_message': error_message,
            'summary': {
                'total_count': 0,
                'by_page': {},
                'by_method': {},
                'average_confidence': 0.0
            }
        }
    
    def enhance_requirements_with_context(
        self, 
        requirements_data: Dict[str, Any], 
        document_text: str
    ) -> Dict[str, Any]:
        """
        Enhance extracted requirements with additional context using LLM
        
        Args:
            requirements_data: Previously extracted requirements
            document_text: Original document text
            
        Returns:
            Enhanced requirements data
        """
        try:
            if not requirements_data.get('requirements'):
                return requirements_data
            
            logger.info("Enhancing requirements with additional context")
            
            # Create enhancement prompt
            prompt = self._create_enhancement_prompt(requirements_data, document_text)
            
            # Get enhancement from LLM
            response = self.watsonx_llm.generate_with_retry(prompt)
            
            # Parse and apply enhancements
            enhanced_data = self._apply_enhancements(requirements_data, response)
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"Requirements enhancement failed: {str(e)}")
            return requirements_data  # Return original if enhancement fails
    
    def _create_enhancement_prompt(
        self, 
        requirements_data: Dict[str, Any], 
        document_text: str
    ) -> str:
        """Create prompt for enhancing requirements with context"""
        
        requirements_text = "\n".join([
            f"{i+1}. {req['requirement']}" 
            for i, req in enumerate(requirements_data['requirements'])
        ])
        
        prompt = f"""以下の建設工事仕様書から抽出された要求事項について、文書の文脈を考慮して詳細情報を補完してください。

抽出済み要求事項:
{requirements_text}

元の文書（関連部分）:
{document_text[:4000]}

以下のJSON形式で、各要求事項に追加の詳細情報を提供してください：

{{
    "enhanced_requirements": [
        {{
            "index": 要求事項の番号,
            "additional_details": "追加の詳細情報",
            "related_context": "関連する文脈情報",
            "priority": "高/中/低"
        }}
    ]
}}

JSON:"""
        
        return prompt
    
    def _apply_enhancements(
        self, 
        original_data: Dict[str, Any], 
        enhancement_response: str
    ) -> Dict[str, Any]:
        """Apply enhancements to original requirements data"""
        try:
            # Parse enhancement response
            json_match = re.search(r'\{.*\}', enhancement_response, re.DOTALL)
            if not json_match:
                return original_data
            
            enhancements = json.loads(json_match.group())
            enhanced_reqs = enhancements.get('enhanced_requirements', [])
            
            # Apply enhancements to requirements
            requirements = original_data['requirements'].copy()
            
            for enhancement in enhanced_reqs:
                try:
                    idx = enhancement.get('index', 0) - 1  # Convert to 0-based index
                    if 0 <= idx < len(requirements):
                        req = requirements[idx]
                        
                        # Add additional details
                        additional_details = enhancement.get('additional_details', '')
                        if additional_details:
                            existing_details = req.get('details', '')
                            combined_details = f"{existing_details} | {additional_details}" if existing_details else additional_details
                            req['details'] = combined_details
                        
                        # Add context and priority
                        req['related_context'] = enhancement.get('related_context', '')
                        req['priority'] = enhancement.get('priority', '')
                        
                except Exception as e:
                    logger.debug(f"Error applying enhancement {enhancement}: {str(e)}")
                    continue
            
            # Update data
            enhanced_data = original_data.copy()
            enhanced_data['requirements'] = requirements
            enhanced_data['enhancement_applied'] = True
            
            logger.info("Successfully applied requirement enhancements")
            return enhanced_data
            
        except Exception as e:
            logger.error(f"Error applying enhancements: {str(e)}")
            return original_data