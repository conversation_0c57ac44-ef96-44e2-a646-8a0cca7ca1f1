"""
Table detection functionality for PDF documents.
"""

import logging
from typing import List, Dict, Any
import pandas as pd

try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False
    logging.warning("Camelot not available. Install with: pip install camelot-py[cv]")

try:
    import tabula
    TABULA_AVAILABLE = True
except ImportError:
    TABULA_AVAILABLE = False
    logging.warning("Tabula not available. Install with: pip install tabula-py")

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False
    logging.warning("PDFPlumber not available. Install with: pip install pdfplumber")

from utils.config import TableDetectionConfig
from utils.models import TableInfo
from utils.extraction_utils import normalize_table_data, contains_japanese_keywords

logger = logging.getLogger(__name__)


class TableDetector:
    """Detects presence of tables in PDF documents"""
    
    def __init__(self, config: TableDetectionConfig = None):
        """
        Initialize table detector
        
        Args:
            config: Table detection configuration
        """
        self.config = config or TableDetectionConfig()
        logger.info("TableDetector initialized")
    
    def detect_tables_in_pdf(self, pdf_path: str) -> List[TableInfo]:
        """
        Detect tables in PDF using multiple methods
        
        Args:
            pdf_path: Path to PDF file
            
        Returns:
            List of detected table information
        """
        logger.info(f"Starting table detection for: {pdf_path}")
        detected_tables = []
        
        try:
            # Method 1: Using camelot for table detection
            if CAMELOT_AVAILABLE:
                tables_camelot = self._detect_with_camelot(pdf_path)
                detected_tables.extend(tables_camelot)
            
            # Method 2: Using tabula for table detection
            if TABULA_AVAILABLE:
                tables_tabula = self._detect_with_tabula(pdf_path)
                detected_tables.extend(tables_tabula)
            
            # Method 3: Using pdfplumber for text-based detection
            if PDFPLUMBER_AVAILABLE:
                tables_pdfplumber = self._detect_with_pdfplumber(pdf_path)
                detected_tables.extend(tables_pdfplumber)
            
        except Exception as e:
            logger.error(f"Error detecting tables: {str(e)}")
        
        # Filter and return relevant tables
        relevant_tables = self._filter_relevant_tables(detected_tables)
        logger.info(f"Found {len(relevant_tables)} relevant tables")
        
        return relevant_tables
    
    def _detect_with_camelot(self, pdf_path: str) -> List[TableInfo]:
        """Detect tables using camelot"""
        try:
            logger.debug("Attempting table detection with Camelot")
            tables = camelot.read_pdf(pdf_path, pages='all')
            
            table_infos = []
            for i, table in enumerate(tables):
                # Normalize the dataframe
                normalized_df = normalize_table_data(table.df)
                
                table_info = TableInfo(
                    method='camelot',
                    page=i + 1,  # Note: This might not be accurate page number
                    table_data=normalized_df,
                    confidence=table.accuracy
                )
                table_infos.append(table_info)
            
            logger.info(f"Camelot detected {len(table_infos)} tables")
            return table_infos
            
        except Exception as e:
            logger.warning(f"Camelot detection failed: {str(e)}")
            return []
    
    def _detect_with_tabula(self, pdf_path: str) -> List[TableInfo]:
        """Detect tables using tabula"""
        try:
            logger.debug("Attempting table detection with Tabula")
            tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)
            
            table_infos = []
            for i, table in enumerate(tables):
                if table.empty:
                    continue
                
                # Normalize the dataframe
                normalized_df = normalize_table_data(table)
                
                table_info = TableInfo(
                    method='tabula',
                    page=i + 1,  # Note: This might not be accurate page number
                    table_data=normalized_df,
                    confidence=0.8  # Default confidence for tabula
                )
                table_infos.append(table_info)
            
            logger.info(f"Tabula detected {len(table_infos)} tables")
            return table_infos
            
        except Exception as e:
            logger.warning(f"Tabula detection failed: {str(e)}")
            return []
    
    def _detect_with_pdfplumber(self, pdf_path: str) -> List[TableInfo]:
        """Detect table-like structures using pdfplumber"""
        try:
            logger.debug("Attempting table detection with PDFPlumber")
            tables_found = []
            
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # Extract text to check for table indicators
                    text = page.extract_text()
                    
                    if self._contains_table_indicators(text):
                        # Try to extract table
                        table = page.extract_table()
                        if table and len(table) > 1:  # Must have header + at least one row
                            try:
                                # Convert to DataFrame
                                df = pd.DataFrame(table[1:], columns=table[0])
                                normalized_df = normalize_table_data(df)
                                
                                if not normalized_df.empty:
                                    table_info = TableInfo(
                                        method='pdfplumber',
                                        page=page_num + 1,
                                        table_data=normalized_df,
                                        confidence=0.7
                                    )
                                    tables_found.append(table_info)
                            except Exception as e:
                                logger.debug(f"Error processing table on page {page_num + 1}: {str(e)}")
                                continue
            
            logger.info(f"PDFPlumber detected {len(tables_found)} tables")
            return tables_found
            
        except Exception as e:
            logger.warning(f"PDFPlumber detection failed: {str(e)}")
            return []
    
    def _contains_table_indicators(self, text: str) -> bool:
        """Check if text contains indicators of requirement tables"""
        if not text:
            return False
        
        return contains_japanese_keywords(
            text, 
            self.config.japanese_table_keywords
        )
    
    def _filter_relevant_tables(self, tables: List[TableInfo]) -> List[TableInfo]:
        """Filter tables that likely contain requirement information"""
        relevant_tables = []
        
        for table_info in tables:
            try:
                if not hasattr(table_info, 'table_data') or table_info.table_data is None:
                    continue
                
                df = table_info.table_data
                if not isinstance(df, pd.DataFrame) or df.empty:
                    continue
                
                # Check if table contains requirement-related keywords
                table_text = df.to_string()
                if self._contains_table_indicators(table_text):
                    # Additional filtering based on table structure
                    if self._is_valid_requirements_table(df):
                        relevant_tables.append(table_info)
                        logger.debug(f"Added relevant table from page {table_info.page} (method: {table_info.method})")
                
            except Exception as e:
                logger.warning(f"Error filtering table: {str(e)}")
                continue
        
        return relevant_tables
    
    def _is_valid_requirements_table(self, df: pd.DataFrame) -> bool:
        """
        Check if DataFrame represents a valid requirements table
        
        Args:
            df: DataFrame to check
            
        Returns:
            True if valid requirements table
        """
        try:
            # Must have at least 2 rows and 2 columns
            if df.shape[0] < 2 or df.shape[1] < 2:
                return False
            
            # Check for requirement-related column headers
            column_text = ' '.join([str(col) for col in df.columns])
            
            requirement_indicators = [
                "要求事項", "立会", "項目", "内容", "作業", "工事"
            ]
            
            has_requirement_column = any(
                indicator in column_text 
                for indicator in requirement_indicators
            )
            
            if not has_requirement_column:
                return False
            
            # Check if table has reasonable amount of content
            non_empty_cells = df.notna().sum().sum()
            total_cells = df.shape[0] * df.shape[1]
            
            if non_empty_cells / total_cells < 0.3:  # At least 30% filled
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Table validation error: {str(e)}")
            return False
    
    def get_detection_summary(self, tables: List[TableInfo]) -> Dict[str, Any]:
        """
        Get summary of detection results
        
        Args:
            tables: List of detected tables
            
        Returns:
            Summary information
        """
        if not tables:
            return {
                'total_tables': 0,
                'by_method': {},
                'by_page': {},
                'average_confidence': 0.0
            }
        
        by_method = {}
        by_page = {}
        confidences = []
        
        for table in tables:
            # Count by method
            method = table.method
            by_method[method] = by_method.get(method, 0) + 1
            
            # Count by page
            page = table.page
            by_page[page] = by_page.get(page, 0) + 1
            
            # Collect confidence scores
            confidences.append(table.confidence)
        
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        return {
            'total_tables': len(tables),
            'by_method': by_method,
            'by_page': by_page,
            'average_confidence': round(avg_confidence, 3)
        }