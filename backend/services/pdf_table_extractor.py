import os
from PyPDF2 import PdfReader
import json
from dotenv import load_dotenv
from ibm_watson_machine_learning.foundation_models import Model
from ibm_watson_machine_learning.metanames import GenTextParamsMetaNames as GenParams
# Load environment variables
load_dotenv(override=True)
api_key = os.getenv("WATSONX_API_KEY", None)
ibm_cloud_url = os.getenv("IBM_CLOUD_URL", None)
project_id = os.getenv("WATSONX_PROJECT_ID", None)

if api_key is None or ibm_cloud_url is None or project_id is None:
    raise Exception("Missing API Key, IBM Cloud URL, or Project ID.")

# Set up Watsonx.ai credentials
creds = {
    "url": ibm_cloud_url,
    "apikey": api_key
}
print("Credentials loaded successfully.")

def send_to_watsonxai(prompts,
                      model_name="meta-llama/llama-3-3-70b-instruct",
                      decoding_method="greedy",
                      max_new_tokens=8192,
                      min_new_tokens=1,
                      temperature=0.3,
                      repetition_penalty=1.2,
                    ):
    """
    Helper function to send prompts and parameters to Watsonx.ai LLM.

    Args:
        prompts (list): List of text prompts.
        decoding_method (str): "sample" or "greedy".
        max_new_tokens (int): Max tokens to generate.
        min_new_tokens (int): Minimum new tokens.
        temperature (float): LLM temperature for creativity.
        repetition_penalty (float): Penalty for repetition.

    Returns:
        str: Generated response.
    """

    assert not any(map(lambda prompt: len(prompt) < 1, prompts)), "Empty prompt detected!"

    # Define model parameters
    model_params = {
        GenParams.DECODING_METHOD: decoding_method,
        GenParams.MIN_NEW_TOKENS: min_new_tokens,
        GenParams.MAX_NEW_TOKENS: max_new_tokens,
        GenParams.RANDOM_SEED: 42,
        GenParams.TEMPERATURE: temperature,
        GenParams.REPETITION_PENALTY: repetition_penalty,
    }

    # Instantiate LLM Model
    model = Model(
        model_id=model_name,
        params=model_params,
        credentials=creds,
        project_id=project_id
    )

    # Call the model with prompt
    for prompt in prompts:
        return model.generate_text(prompt)

def extract_tables_from_pdf(pdf_path):
    """Extract specific tables from a Japanese PDF using watsonx LLM."""
    # Read PDF and extract content
    with open(pdf_path, "rb") as file:
        pdf_reader = PdfReader(file)
        text_content = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text_content += page.extract_text()
    
    # Prepare prompt for specific table extraction
    prompt = f"""
    Extract only the tables from the following Japanese PDF content and format them as JSON.
    
    Focus specifically on tables that have consistent headers across documents, such as:
    - Tables with requirements (要求事項)
    - Tables with equipment lists (like the ABC消火器 table)
    
    For each table, provide:
    1. Table headers in Japanese
    2. All rows of data
    3. Page number where the table appears
    
    Ignore any text that is not part of these specific tables.
    
    PDF Content:
    {text_content[:8000]}
    """
    
    # Use the provided function to call watsonx
    response = send_to_watsonxai(
        prompts=[prompt],
        model_name="meta-llama/llama-3-3-70b-instruct",
        decoding_method="greedy",
        max_new_tokens=2000,
        min_new_tokens=1,
        temperature=0.2,  # Lower temperature for more precise extraction
        repetition_penalty=1.2
    )
    
    return response

if __name__ == "__main__":
    pdf_file = input("Enter the path to your PDF file: ")
    tables = extract_tables_from_pdf(pdf_file)
    print(tables)
    
    # Save to file with the PDF name
    output_filename = os.path.splitext(os.path.basename(pdf_file))[0] + "_tables.json"
    with open(output_filename, "w", encoding="utf-8") as f:
        f.write(tables)
    print(f"Tables saved to {output_filename}")
