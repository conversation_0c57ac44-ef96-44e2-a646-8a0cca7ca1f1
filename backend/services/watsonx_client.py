"""
WatsonX LLM client for requirements extraction.
"""

import requests
import json
import logging
import time
from typing import Dict, Any, Optional

from utils.config import WatsonXConfig
from utils.extraction_utils import retry_with_backoff

logger = logging.getLogger(__name__)


class WatsonXLLM:
    """WatsonX LLM integration for requirements extraction"""
    
    def __init__(self, config: WatsonXConfig):
        """
        Initialize WatsonX client
        
        Args:
            config: WatsonX configuration
        """
        self.config = config
        self.access_token = None
        self.token_expires_at = 0
        
        # Validate configuration
        if not config.api_key or not config.project_id:
            raise ValueError("API key and project ID are required")
    
    def _get_access_token(self) -> str:
        """Get or refresh access token"""
        current_time = time.time()
        
        # Check if token is still valid (with 5-minute buffer)
        if self.access_token and current_time < (self.token_expires_at - 300):
            return self.access_token
        
        # Get new token
        auth_url = "https://iam.cloud.ibm.com/identity/token"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        data = {
            "grant_type": "urn:ibm:params:oauth:grant-type:apikey",
            "apikey": self.config.api_key
        }
        
        try:
            logger.info("Requesting new WatsonX access token")
            response = requests.post(auth_url, headers=headers, data=data, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data["access_token"]
            # Token typically expires in 1 hour
            self.token_expires_at = current_time + token_data.get("expires_in", 3600)
            
            logger.info("Successfully obtained WatsonX access token")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to get access token: {str(e)}")
            raise
        except KeyError as e:
            logger.error(f"Invalid token response format: {str(e)}")
            raise
    
    def generate(self, prompt: str, **kwargs) -> str:
        """
        Generate text using WatsonX LLM
        
        Args:
            prompt: Input prompt for the model
            **kwargs: Additional parameters for generation
            
        Returns:
            Generated text response
        """
        try:
            # Get access token
            token = self._get_access_token()
            
            # Prepare request
            url = f"{self.config.base_url}/ml/v1/text/generation?version=2023-05-29"
            
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            
            # Merge default config with kwargs
            generation_params = {
                "max_new_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "top_p": kwargs.get("top_p", 0.9),
                "top_k": kwargs.get("top_k", 50),
                "repetition_penalty": kwargs.get("repetition_penalty", 1.1),
                "stop_sequences": kwargs.get("stop_sequences", [])
            }
            
            payload = {
                "input": prompt,
                "parameters": generation_params,
                "model_id": kwargs.get("model_id", self.config.model_id),
                "project_id": self.config.project_id
            }
            
            logger.debug(f"Sending WatsonX request with {len(prompt)} characters")
            
            # Make request
            response = requests.post(
                url, 
                headers=headers, 
                json=payload, 
                timeout=self.config.timeout
            )
            response.raise_for_status()
            
            # Extract generated text
            result = response.json()
            generated_text = result["results"][0]["generated_text"]
            
            logger.info(f"Successfully generated {len(generated_text)} characters")
            return generated_text.strip()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"WatsonX API request failed: {str(e)}")
            raise
        except KeyError as e:
            logger.error(f"Unexpected response format: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"WatsonX generation failed: {str(e)}")
            raise
    
    @retry_with_backoff
    def generate_with_retry(self, prompt: str, max_retries: int = 3, **kwargs) -> str:
        """
        Generate text with retry logic
        
        Args:
            prompt: Input prompt
            max_retries: Maximum number of retry attempts
            **kwargs: Additional parameters
            
        Returns:
            Generated text response
        """
        return self.generate(prompt, **kwargs)
    
    def test_connection(self) -> bool:
        """
        Test the connection to WatsonX
        
        Returns:
            True if connection successful
        """
        try:
            test_prompt = "こんにちは。テストメッセージです。"
            response = self.generate(test_prompt, max_tokens=50)
            logger.info("WatsonX connection test successful")
            return True
        except Exception as e:
            logger.error(f"WatsonX connection test failed: {str(e)}")
            return False
    
    def validate_config(self) -> bool:
        """
        Validate the configuration
        
        Returns:
            True if configuration is valid
        """
        try:
            # Check required fields
            if not self.config.api_key:
                logger.error("API key is missing")
                return False
            
            if not self.config.project_id:
                logger.error("Project ID is missing")
                return False
            
            # Test token acquisition
            token = self._get_access_token()
            if not token:
                logger.error("Failed to obtain access token")
                return False
            
            logger.info("WatsonX configuration is valid")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {str(e)}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the configured model
        
        Returns:
            Model information dictionary
        """
        return {
            'model_id': self.config.model_id,
            'base_url': self.config.base_url,
            'max_tokens': self.config.max_tokens,
            'temperature': self.config.temperature,
            'timeout': self.config.timeout
        }