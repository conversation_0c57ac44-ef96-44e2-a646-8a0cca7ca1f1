"""
Table extraction functionality for requirements information.
"""

import logging
import re
from typing import Dict, List, Any, Optional
import pandas as pd

from utils.models import TableInfo, Requirement
from utils.extraction_utils import create_summary_stats

logger = logging.getLogger(__name__)


class TableExtractor:
    """Extracts requirement information from detected tables"""
    
    def __init__(self):
        """Initialize table extractor"""
        self.requirement_patterns = [
            r"要求事項",
            r"立会項目", 
            r"工事.*要求",
            r"作業.*要求",
            r"実施.*事項"
        ]
        
        self.witness_patterns = [
            r"立会",
            r"立合",
            r"確認"
        ]
        
        self.item_patterns = [
            r"項目",
            r"番号",
            r"No\.",
            r"（.*）"
        ]
        
        logger.info("TableExtractor initialized")
    
    def extract_requirements_from_tables(self, tables: List[TableInfo]) -> Dict[str, Any]:
        """
        Extract requirements from detected tables
        
        Args:
            tables: List of detected table information
            
        Returns:
            Extracted requirements data
        """
        if not tables:
            logger.warning("No tables provided for extraction")
            return {}
        
        logger.info(f"Processing {len(tables)} tables for requirement extraction")
        all_requirements = []
        
        for i, table_info in enumerate(tables):
            try:
                logger.debug(f"Processing table {i+1}/{len(tables)} from page {table_info.page}")
                requirements = self._process_single_table(table_info)
                if requirements:
                    all_requirements.extend(requirements)
                    logger.debug(f"Extracted {len(requirements)} requirements from table")
            except Exception as e:
                logger.error(f"Error processing table {i+1}: {str(e)}")
                continue
        
        return self._consolidate_requirements(all_requirements)
    
    def _process_single_table(self, table_info: TableInfo) -> List[Requirement]:
        """Process a single table to extract requirements"""
        df = table_info.table_data
        
        if df.empty:
            return []
        
        # Find columns that contain requirement information
        column_mapping = self._identify_requirement_columns(df)
        
        if not column_mapping:
            logger.debug(f"No requirement columns found in table from page {table_info.page}")
            return []
        
        logger.debug(f"Found column mapping: {column_mapping}")
        
        requirements = []
        for idx, row in df.iterrows():
            try:
                requirement = self._extract_row_requirements(row, column_mapping, table_info)
                if requirement and requirement.requirement.strip():
                    requirements.append(requirement)
            except Exception as e:
                logger.debug(f"Error processing row {idx}: {str(e)}")
                continue
        
        return requirements
    
    def _identify_requirement_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """Identify which columns contain requirement information"""
        column_mapping = {}
        
        for col in df.columns:
            col_str = str(col).strip()
            col_lower = col_str.lower()
            
            # Check for requirement columns
            if any(re.search(pattern, col_str) for pattern in self.requirement_patterns):
                if "要求事項" in col_str or "要求" in col_str:
                    column_mapping['requirement'] = col
                elif "作業" in col_str:
                    column_mapping['requirement'] = col
            
            # Check for witness columns  
            elif any(re.search(pattern, col_str) for pattern in self.witness_patterns):
                column_mapping['witness'] = col
            
            # Check for item/number columns
            elif any(re.search(pattern, col_str) for pattern in self.item_patterns):
                column_mapping['item'] = col
        
        # If no explicit requirement column found, use heuristics
        if 'requirement' not in column_mapping:
            column_mapping = self._use_heuristic_column_detection(df)
        
        return column_mapping
    
    def _use_heuristic_column_detection(self, df: pd.DataFrame) -> Dict[str, str]:
        """Use heuristics to detect columns when patterns don't match"""
        column_mapping = {}
        
        # Analyze column content to identify requirement columns
        for col in df.columns:
            try:
                # Convert column to string and check content
                col_content = df[col].astype(str).str.cat(sep=' ')
                
                # Check if column contains requirement-like content
                req_keywords = ["行う", "実施", "確認", "点検", "試験", "検査", "遵守"]
                keyword_count = sum(1 for keyword in req_keywords if keyword in col_content)
                
                if keyword_count >= 2:
                    if 'requirement' not in column_mapping:
                        column_mapping['requirement'] = col
                
                # Check for witness indicators
                if any(indicator in col_content for indicator in ["○", "×", "立会", "確認"]):
                    if 'witness' not in column_mapping:
                        column_mapping['witness'] = col
                
                # Check for item numbers
                if re.search(r'[\(（]\d+[\)）]', col_content) or re.search(r'\d+\.', col_content):
                    if 'item' not in column_mapping:
                        column_mapping['item'] = col
                        
            except Exception as e:
                logger.debug(f"Error in heuristic detection for column {col}: {str(e)}")
                continue
        
        return column_mapping
    
    def _extract_row_requirements(
        self, 
        row: pd.Series, 
        column_mapping: Dict[str, str], 
        table_info: TableInfo
    ) -> Optional[Requirement]:
        """Extract requirements from a single table row"""
        
        requirement_text = ""
        witness = None
        item = None
        details = ""
        
        # Extract requirement text
        if 'requirement' in column_mapping:
            req_col = column_mapping['requirement']
            if req_col in row.index:
                requirement_text = str(row[req_col]).strip()
        
        # Extract witness information
        if 'witness' in column_mapping:
            witness_col = column_mapping['witness']
            if witness_col in row.index:
                witness_val = str(row[witness_col]).strip()
                if witness_val and witness_val.lower() not in ['nan', 'none', '']:
                    witness = witness_val
        
        # Extract item information
        if 'item' in column_mapping:
            item_col = column_mapping['item']
            if item_col in row.index:
                item_val = str(row[item_col]).strip()
                if item_val and item_val.lower() not in ['nan', 'none', '']:
                    item = item_val
        
        # Collect additional details from other columns
        other_details = []
        for col in row.index:
            if col not in column_mapping.values():
                val = str(row[col]).strip()
                if val and val.lower() not in ['nan', 'none', '']:
                    other_details.append(f"{col}: {val}")
        
        if other_details:
            details = " | ".join(other_details)
        
        # Skip if no meaningful requirement text
        if not requirement_text or requirement_text.lower() in ['nan', 'none', '']:
            return None
        
        return Requirement(
            requirement=requirement_text,
            witness=witness,
            item=item,
            details=details if details else None,
            source_page=table_info.page,
            extraction_method=table_info.method,
            confidence=table_info.confidence
        )
    
    def _consolidate_requirements(self, requirements: List[Requirement]) -> Dict[str, Any]:
        """Consolidate all extracted requirements"""
        if not requirements:
            return {
                'total_requirements': 0,
                'requirements': [],
                'summary': {}
            }
        
        # Convert requirements to dictionaries
        requirements_dicts = [req.to_dict() for req in requirements]
        
        # Remove duplicates based on requirement text
        unique_requirements = []
        seen_requirements = set()
        
        for req_dict in requirements_dicts:
            req_text = req_dict['requirement'].strip().lower()
            if req_text not in seen_requirements:
                seen_requirements.add(req_text)
                unique_requirements.append(req_dict)
        
        # Create summary
        summary = create_summary_stats(unique_requirements)
        
        consolidated = {
            'total_requirements': len(unique_requirements),
            'requirements': unique_requirements,
            'summary': summary,
            'extraction_metadata': {
                'duplicates_removed': len(requirements_dicts) - len(unique_requirements),
                'original_count': len(requirements_dicts)
            }
        }
        
        logger.info(f"Consolidated {len(requirements_dicts)} requirements into {len(unique_requirements)} unique items")
        
        return consolidated
    
    def validate_extracted_requirements(self, requirements_data: Dict[str, Any]) -> bool:
        """
        Validate extracted requirements data
        
        Args:
            requirements_data: Extracted requirements data
            
        Returns:
            True if valid
        """
        try:
            # Check basic structure
            if not isinstance(requirements_data, dict):
                return False
            
            if 'requirements' not in requirements_data:
                return False
            
            requirements = requirements_data['requirements']
            if not isinstance(requirements, list):
                return False
            
            # Check each requirement
            for req in requirements:
                if not isinstance(req, dict):
                    return False
                
                if 'requirement' not in req:
                    return False
                
                if not req['requirement'] or not req['requirement'].strip():
                    return False
            
            logger.debug("Requirements validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Requirements validation failed: {str(e)}")
            return False