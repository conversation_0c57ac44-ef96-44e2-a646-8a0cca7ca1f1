"""
Main pipeline for extracting requirements from construction specification documents.
"""

import json
import logging
import time
from typing import Dict, Any, Optional

from utils.config import Config
from utils.models import ExtractionResult, ExtractionMethod
from services.watsonx_client import WatsonXLLM
from services.table_detector import TableDetector
from services.table_extractor import TableExtractor
from services.llm_extractor import LLMExtractor
from utils.extraction_utils import extract_text_from_pdf, export_to_json, calculate_confidence_score

logger = logging.getLogger(__name__)


class RequirementsExtractionPipeline:
    """Main pipeline for extracting requirements from construction specification documents"""
    
    def __init__(self, config: Config):
        """
        Initialize the extraction pipeline
        
        Args:
            config: Configuration object
        """
        self.config = config
        
        # Initialize WatsonX LLM
        self.watsonx_llm = WatsonXLLM(config.watsonx)
        
        # Initialize extraction components
        self.table_detector = TableDetector(config.table_detection)
        self.table_extractor = TableExtractor()
        self.llm_extractor = LLMExtractor(self.watsonx_llm)
        
        logger.info("RequirementsExtractionPipeline initialized")
    
    def extract_requirements(
        self, 
        pdf_path: str, 
        document_text: Optional[str] = None
    ) -> ExtractionResult:
        """
        Main extraction pipeline
        
        Args:
            pdf_path: Path to PDF file
            document_text: Optional pre-extracted text
            
        Returns:
            ExtractionResult with extracted requirements
        """
        start_time = time.time()
        logger.info(f"Starting requirement extraction for: {pdf_path}")
        
        try:
            # Step 1: Try table-based extraction
            logger.info("Attempting table-based extraction")
            table_result = self._attempt_table_extraction(pdf_path)
            
            if (table_result.success and 
                table_result.confidence_score >= self.config.extraction.table_confidence_threshold):
                logger.info(f"Table extraction successful with confidence {table_result.confidence_score:.3f}")
                table_result.data['processing_time_seconds'] = time.time() - start_time
                return table_result
            
            # Step 2: Fallback to LLM extraction
            if self.config.extraction.llm_fallback_enabled:
                logger.info("Falling back to LLM extraction")
                llm_result = self._attempt_llm_extraction(pdf_path, document_text)
                llm_result.data['processing_time_seconds'] = time.time() - start_time
                return llm_result
            else:
                logger.warning("LLM fallback disabled, returning table result")
                table_result.data['processing_time_seconds'] = time.time() - start_time
                return table_result
            
        except Exception as e:
            logger.error(f"Pipeline execution failed: {str(e)}")
            return ExtractionResult(
                method_used=ExtractionMethod.LLM_EXTRACTION,
                success=False,
                data={'error_details': str(e), 'processing_time_seconds': time.time() - start_time},
                confidence_score=0.0,
                error_message=str(e)
            )
    
    def _attempt_table_extraction(self, pdf_path: str) -> ExtractionResult:
        """Attempt to extract requirements using table detection"""
        try:
            logger.debug("Starting table detection")
            
            # Detect tables
            detected_tables = self.table_detector.detect_tables_in_pdf(pdf_path)
            
            if not detected_tables:
                logger.info("No tables detected in document")
                return ExtractionResult(
                    method_used=ExtractionMethod.TABLE_EXTRACTION,
                    success=False,
                    data={'detection_summary': {'total_tables': 0}},
                    confidence_score=0.0,
                    error_message="No tables detected"
                )
            
            logger.info(f"Detected {len(detected_tables)} tables")
            
            # Extract requirements from tables
            extracted_data = self.table_extractor.extract_requirements_from_tables(detected_tables)
            
            if not extracted_data or extracted_data.get('total_requirements', 0) == 0:
                logger.info("No requirements found in detected tables")
                return ExtractionResult(
                    method_used=ExtractionMethod.TABLE_EXTRACTION,
                    success=False,
                    data={
                        'detection_summary': self.table_detector.get_detection_summary(detected_tables),
                        'extraction_attempted': True
                    },
                    confidence_score=0.0,
                    error_message="No requirements found in tables"
                )
            
            # Calculate confidence score
            confidence = self._calculate_table_confidence(extracted_data, detected_tables)
            
            # Add detection summary to extracted data
            extracted_data['detection_summary'] = self.table_detector.get_detection_summary(detected_tables)
            
            logger.info(f"Table extraction completed with {extracted_data['total_requirements']} requirements")
            
            return ExtractionResult(
                method_used=ExtractionMethod.TABLE_EXTRACTION,
                success=True,
                data=extracted_data,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Table extraction error: {str(e)}")
            return ExtractionResult(
                method_used=ExtractionMethod.TABLE_EXTRACTION,
                success=False,
                data={'error_details': str(e)},
                confidence_score=0.0,
                error_message=f"Table extraction error: {str(e)}"
            )
    
    def _attempt_llm_extraction(
        self, 
        pdf_path: str, 
        document_text: Optional[str] = None
    ) -> ExtractionResult:
        """Attempt to extract requirements using LLM"""
        try:
            logger.debug("Starting LLM extraction")
            
            # Extract text if not provided
            if document_text is None:
                logger.debug("Extracting text from PDF")
                document_text = extract_text_from_pdf(pdf_path)
            
            if not document_text:
                logger.error("No text could be extracted from PDF")
                return ExtractionResult(
                    method_used=ExtractionMethod.LLM_EXTRACTION,
                    success=False,
                    data={'error_details': 'No text extracted from PDF'},
                    confidence_score=0.0,
                    error_message="No text could be extracted from PDF"
                )
            
            logger.info(f"Extracted {len(document_text)} characters from PDF")
            
            # Extract requirements using LLM
            extracted_data = self.llm_extractor.extract_requirements_with_llm(document_text)
            
            if not extracted_data or extracted_data.get('total_count', 0) == 0:
                logger.warning("LLM extraction returned no requirements")
                return ExtractionResult(
                    method_used=ExtractionMethod.LLM_EXTRACTION,
                    success=False,
                    data=extracted_data or {'error_details': 'No data returned'},
                    confidence_score=0.0,
                    error_message="LLM extraction returned no requirements"
                )
            
            # Get confidence from LLM response or use default
            confidence = extracted_data.get('extraction_confidence', 0.7)
            
            # Enhance requirements with additional context if configured
            if len(extracted_data.get('requirements', [])) > 0:
                try:
                    enhanced_data = self.llm_extractor.enhance_requirements_with_context(
                        extracted_data, 
                        document_text
                    )
                    if enhanced_data != extracted_data:
                        logger.info("Requirements enhanced with additional context")
                        extracted_data = enhanced_data
                except Exception as e:
                    logger.warning(f"Requirements enhancement failed: {str(e)}")
            
            logger.info(f"LLM extraction completed with {extracted_data['total_count']} requirements")
            
            return ExtractionResult(
                method_used=ExtractionMethod.LLM_EXTRACTION,
                success=True,
                data=extracted_data,
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"LLM extraction error: {str(e)}")
            return ExtractionResult(
                method_used=ExtractionMethod.LLM_EXTRACTION,
                success=False,
                data={'error_details': str(e)},
                confidence_score=0.0,
                error_message=f"LLM extraction error: {str(e)}"
            )
    
    def _calculate_table_confidence(
        self, 
        extracted_data: Dict[str, Any], 
        detected_tables: list
    ) -> float:
        """Calculate confidence score for table extraction"""
        if not extracted_data or not detected_tables:
            return 0.0
        
        # Base confidence from table detection
        table_confidences = [t.confidence for t in detected_tables]
        avg_table_confidence = sum(table_confidences) / len(table_confidences)
        
        # Boost confidence based on number of requirements found
        requirement_count = extracted_data.get('total_requirements', 0)
        
        # Use utility function for confidence calculation
        method_reliability = {'table_extraction': 1.0}
        
        final_confidence = calculate_confidence_score(
            avg_table_confidence,
            requirement_count,
            method_reliability
        )
        
        return final_confidence
    
    def validate_extraction_result(self, result: ExtractionResult) -> ExtractionResult:
        """
        Validate and potentially enhance extraction result
        
        Args:
            result: Extraction result to validate
            
        Returns:
            Validated extraction result
        """
        if not self.config.extraction.enable_validation:
            return result
        
        try:
            logger.debug("Validating extraction result")
            
            if not result.success:
                return result
            
            data = result.data
            
            # Validation checks
            if not data:
                result.success = False
                result.error_message = "Empty extraction result"
                return result
            
            # Check for minimum requirements
            if isinstance(data, dict):
                requirements = data.get('requirements', [])
                
                if not requirements:
                    result.confidence_score *= 0.5  # Reduce confidence
                    logger.warning("No requirements found in extraction result")
                
                # Validate each requirement
                valid_requirements = []
                for req in requirements:
                    if isinstance(req, dict) and req.get('requirement'):
                        req_text = str(req['requirement']).strip()
                        if req_text and req_text.lower() not in ['nan', 'none', '']:
                            valid_requirements.append(req)
                
                # Update data with valid requirements
                data['requirements'] = valid_requirements
                data['total_requirements'] = len(valid_requirements)
                data['total_count'] = len(valid_requirements)  # For LLM compatibility
                
                # Add validation metadata
                data['validation'] = {
                    'validated_at': time.time(),
                    'validation_passed': True,
                    'valid_requirement_count': len(valid_requirements),
                    'original_requirement_count': len(requirements)
                }
                
                logger.debug(f"Validation completed: {len(valid_requirements)}/{len(requirements)} requirements valid")
            
            return result
            
        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
            result.error_message = f"Validation error: {str(e)}"
            return result
    
    def export_to_json(
        self, 
        result: ExtractionResult, 
        output_path: Optional[str] = None
    ) -> str:
        """
        Export extraction result to JSON
        
        Args:
            result: ExtractionResult to export
            output_path: Optional path to save JSON file
            
        Returns:
            JSON string
        """
        try:
            export_data = {
                'extraction_metadata': {
                    'method_used': result.method_used.value,
                    'success': result.success,
                    'confidence_score': result.confidence_score,
                    'error_message': result.error_message,
                    'extraction_timestamp': time.time()
                },
                'extracted_data': result.data
            }
            
            return export_to_json(export_data, output_path, ensure_ascii=False)
            
        except Exception as e:
            logger.error(f"Export failed: {str(e)}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test all connections (primarily WatsonX)
        
        Returns:
            True if all connections successful
        """
        try:
            logger.info("Testing pipeline connections")
            
            # Test WatsonX connection
            if not self.watsonx_llm.test_connection():
                logger.error("WatsonX connection test failed")
                return False
            
            logger.info("All connection tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """
        Get information about the pipeline configuration
        
        Returns:
            Pipeline information dictionary
        """
        return {
            'watsonx_model': self.config.watsonx.model_id,
            'table_confidence_threshold': self.config.extraction.table_confidence_threshold,
            'llm_fallback_enabled': self.config.extraction.llm_fallback_enabled,
            'validation_enabled': self.config.extraction.enable_validation,
            'max_retries': self.config.extraction.max_retries
        }