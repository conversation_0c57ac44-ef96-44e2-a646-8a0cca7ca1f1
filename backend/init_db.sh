#!/bin/bash

# Load environment variables
source /app/.env

# Start PostgreSQL service
sudo service postgresql start

# Wait for PostgreSQL to be ready
until sudo -u postgres pg_isready; do
  echo "Waiting for PostgreSQL to be ready..."
  sleep 2
done

# Create user and database
sudo -u postgres psql -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || true
sudo -u postgres psql -c "ALTER USER $DB_USER CREATEDB;" 2>/dev/null || true
sudo -u postgres psql -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" 2>/dev/null || true
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;" 2>/dev/null || true

echo "PostgreSQL setup completed successfully!"