# ---------- services/box_service.py ----------
import json
import os
import logging
from pathlib import Path
from boxsdk import OAuth2, Client
from boxsdk.exception import BoxAPIException, BoxException
from dotenv import load_dotenv

# Configure logging for this module
logger = logging.getLogger(__name__)

# Load environment variables
try:
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    logger.info(f"Loading .env from box_service: {dotenv_path}")
    load_dotenv(dotenv_path)
    logger.info("Box service .env loaded successfully")
except Exception as e:
    logger.error(f"Failed to load .env in box_service: {e}")
    raise

# Log configuration (without secrets)
logger.info(f"Box service configuration:")
logger.info(f"  BOX_CLIENT_ID: {'[SET]' if os.getenv('BOX_CLIENT_ID') else '[NOT SET]'}")
logger.info(f"  BOX_CLIENT_SECRET: {'[SET]' if os.getenv('BOX_CLIENT_SECRET') else '[NOT SET]'}")
logger.info(f"  ACCESS_TOKEN: {'[SET]' if os.getenv('ACCESS_TOKEN') else '[NOT SET]'}")
logger.info(f"  REFRESH_TOKEN: {'[SET]' if os.getenv('REFRESH_TOKEN') else '[NOT SET]'}")

# Validate required configuration
if not all([os.getenv("BOX_CLIENT_ID"), os.getenv("BOX_CLIENT_SECRET"), os.getenv("ACCESS_TOKEN"), os.getenv("REFRESH_TOKEN")]):
    logger.error("Missing required Box configuration: BOX_CLIENT_ID, BOX_CLIENT_SECRET, ACCESS_TOKEN, and/or REFRESH_TOKEN")
    raise ValueError("Missing required Box configuration")


def store_tokens(new_access_token, new_refresh_token):
    """Store Box tokens - tokens are now managed via environment variables"""
    logger.info("Token refresh callback called - tokens are managed via environment variables")

def upload_to_box(file_path, file_name, folder_id="0"):
    """Upload file to Box with comprehensive error handling and logging"""
    try:
        logger.info(f"Starting Box upload: {file_name}")
        logger.debug(f"File path: {file_path}")
        logger.debug(f"Target folder ID: {folder_id}")
        
        # Validate file exists
        if not os.path.exists(file_path):
            logger.error(f"File to upload does not exist: {file_path}")
            raise FileNotFoundError(f"File not found: {file_path}")
            
        file_size = os.path.getsize(file_path)
        logger.info(f"File size: {file_size} bytes")
        
        # Use tokens from environment variables
        logger.info("Using Box tokens from environment variables")
        
        # Create OAuth client
        logger.info("Creating Box OAuth client...")
        try:
            oauth = OAuth2(
                client_id=os.getenv("BOX_CLIENT_ID"),
                client_secret=os.getenv("BOX_CLIENT_SECRET"),
                access_token=os.getenv("ACCESS_TOKEN"),
                refresh_token=os.getenv("REFRESH_TOKEN"),
                store_tokens=store_tokens
            )
            logger.debug("OAuth client created successfully")
        except Exception as oauth_e:
            logger.error(f"Failed to create OAuth client: {oauth_e}")
            raise
        
        # Create Box client
        logger.info("Creating Box client...")
        try:
            client = Client(oauth)
            logger.debug("Box client created successfully")
        except Exception as client_e:
            logger.error(f"Failed to create Box client: {client_e}")
            raise
            
        # Test Box connection
        try:
            logger.info("Testing Box connection...")
            user = client.user().get()
            logger.info(f"Connected to Box as user: {user.name} ({user.login})")
        except BoxAPIException as api_e:
            logger.error(f"Box API error during connection test: {api_e}")
            logger.error(f"Status code: {api_e.status}")
            logger.error(f"Error message: {api_e.message}")
            raise
        except Exception as conn_e:
            logger.error(f"Connection test failed: {conn_e}")
            raise
        
        # Upload file
        logger.info(f"Uploading file to Box folder {folder_id}...")
        try:
            uploaded_file = client.folder(folder_id).upload(file_path, file_name)
            logger.info(f"File uploaded successfully: {uploaded_file.name}")
            logger.debug(f"Uploaded file ID: {uploaded_file.id}")
        except BoxAPIException as upload_e:
            logger.error(f"Box API error during upload: {upload_e}")
            logger.error(f"Status code: {upload_e.status}")
            logger.error(f"Error message: {upload_e.message}")
            
            # Handle specific error cases
            if upload_e.status == 409:
                logger.error("File with same name already exists in folder")
            elif upload_e.status == 403:
                logger.error("Insufficient permissions to upload to this folder")
            elif upload_e.status == 413:
                logger.error("File too large for upload")
                
            raise
        except Exception as upload_e:
            logger.error(f"Unexpected error during upload: {upload_e}")
            raise
        
        # Get shared link
        logger.info("Creating shared link...")
        try:
            shared_link = uploaded_file.get_shared_link()
            logger.info(f"Shared link created: {shared_link}")
            return shared_link
        except BoxAPIException as link_e:
            logger.error(f"Box API error creating shared link: {link_e}")
            logger.error(f"Status code: {link_e.status}")
            logger.error(f"Error message: {link_e.message}")
            
            # Even if shared link fails, we can return the file info
            logger.warning("Returning file info instead of shared link")
            return f"File uploaded successfully (ID: {uploaded_file.id}, Name: {uploaded_file.name})"
        except Exception as link_e:
            logger.error(f"Unexpected error creating shared link: {link_e}")
            logger.warning("Returning file info instead of shared link")
            return f"File uploaded successfully (ID: {uploaded_file.id}, Name: {uploaded_file.name})"
            
    except BoxException as box_e:
        logger.error(f"Box SDK error: {box_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error in Box upload: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        raise
