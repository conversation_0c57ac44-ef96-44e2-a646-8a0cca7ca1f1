import psycopg2
import os
import logging
from dotenv import load_dotenv
from psycopg2 import OperationalError, DatabaseError

# Configure logging for this module
logger = logging.getLogger(__name__)

# Load environment variables
try:
    dotenv_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    logger.info(f"Loading .env from db_service: {dotenv_path}")
    load_dotenv(dotenv_path)
    logger.info("Database service .env loaded successfully")
except Exception as e:
    logger.error(f"Failed to load .env in db_service: {e}")
    raise

def get_connection(max_retries=30, retry_delay=2):
    """Get database connection with retry logic and error handling"""
    import time
    
    for attempt in range(max_retries):
        try:
            # Get database configuration with connection parameters for Code Engine
            db_config = {
                "dbname": os.getenv("DB_NAME"),
                "user": os.getenv("DB_USER"),
                "password": os.getenv("DB_PASSWORD"),
                "host": os.getenv("DB_HOST", "localhost"),
                "port": os.getenv("DB_PORT", "5432"),
                "connect_timeout": 30,
                "keepalives": 1,
                "keepalives_idle": 30,
                "keepalives_interval": 10,
                "keepalives_count": 5
            }
            
            # Log connection attempt (without password)
            safe_config = {k: v for k, v in db_config.items() if k != "password"}
            safe_config["password"] = "[REDACTED]"
            
            if attempt == 0:
                logger.info(f"Attempting database connection with config: {safe_config}")
            else:
                logger.info(f"Database connection attempt {attempt + 1}/{max_retries}")
            
            # Validate required environment variables
            required_vars = ["DB_NAME", "DB_USER", "DB_PASSWORD"]
            missing_vars = [var for var in required_vars if not os.getenv(var)]
            
            if missing_vars:
                logger.error(f"Missing required environment variables: {missing_vars}")
                raise ValueError(f"Missing required environment variables: {missing_vars}")
            
            # Attempt connection
            logger.debug("Creating database connection...")
            conn = psycopg2.connect(**db_config)
            logger.info("Database connection established successfully")
            
            # Test connection
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    logger.debug(f"Database connection test result: {result}")
            except Exception as test_e:
                logger.warning(f"Database connection test failed: {test_e}")
                
            return conn
            
        except (OperationalError, DatabaseError) as db_e:
            if attempt < max_retries - 1:
                logger.warning(f"Database connection failed (attempt {attempt + 1}/{max_retries}): {db_e}")
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                continue
            else:
                logger.error(f"Database connection failed after {max_retries} attempts: {db_e}")
                logger.error("Check if PostgreSQL is running and credentials are correct")
                raise
        except Exception as e:
            if attempt < max_retries - 1:
                logger.warning(f"Unexpected error connecting to database (attempt {attempt + 1}/{max_retries}): {e}")
                logger.info(f"Retrying in {retry_delay} seconds...")
                time.sleep(retry_delay)
                continue
            else:
                logger.error(f"Unexpected error connecting to database after {max_retries} attempts: {e}")
                logger.error(f"Error type: {type(e).__name__}")
                raise
    
    # This should never be reached, but just in case
    raise Exception("Database connection failed after all retry attempts")

def insert_query_get_id(conn, query, box_link=None):
    """Insert query into database and return the ID with error handling (legacy support)"""
    cur = None
    try:
        logger.info(f"Inserting legacy query to database, length: {len(query)}")
        logger.debug(f"Query preview: {query[:200]}...")
        if box_link:
            logger.debug(f"Box link provided: {box_link}")
        
        cur = conn.cursor()
        logger.debug("Database cursor created")
        
        # For legacy support, insert into issue field and let compiled field auto-generate
        cur.execute("INSERT INTO query_links (issue, box_link, source) VALUES (%s, %s, %s) RETURNING id", 
                   (query, box_link, 'legacy'))
        logger.debug("Insert query executed")
        
        # Get the inserted ID
        result = cur.fetchone()
        if not result:
            logger.error("No ID returned from insert query")
            raise DatabaseError("Insert query did not return an ID")
            
        inserted_id = result[0]
        logger.info(f"Query inserted successfully with ID: {inserted_id}")
        
        # Commit the transaction
        conn.commit()
        logger.debug("Database transaction committed")
        
        return inserted_id
        
    except DatabaseError as db_e:
        logger.error(f"Database error during insert: {db_e}")
        try:
            conn.rollback()
            logger.info("Database transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback transaction: {rollback_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during insert: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        try:
            conn.rollback()
            logger.info("Database transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed")
            except Exception as close_e:
                logger.warning(f"Error closing cursor: {close_e}")

def insert_webmethods_data(conn, date, issue, description, metadata, box_link):
    """Insert webmethods data with separate fields"""
    cur = None
    try:
        logger.info(f"Inserting webmethods data to database")
        logger.debug(f"Date: {date}, Issue: {issue[:100]}..., Description: {description[:100] if description else 'None'}...")
        
        cur = conn.cursor()
        logger.debug("Database cursor created for webmethods")
        
        # Execute insert with separate fields
        cur.execute("""
            INSERT INTO query_links (date, issue, description, metadata, box_link, source) 
            VALUES (%s, %s, %s, %s, %s, %s) RETURNING id
        """, (date, issue, description, metadata, box_link, 'webmethods'))
        logger.debug("WebMethods insert query executed")
        
        # Get the inserted ID
        result = cur.fetchone()
        if not result:
            logger.error("No ID returned from webmethods insert query")
            raise DatabaseError("WebMethods insert query did not return an ID")
            
        inserted_id = result[0]
        logger.info(f"WebMethods data inserted successfully with ID: {inserted_id}")
        
        # Commit the transaction
        conn.commit()
        logger.debug("WebMethods database transaction committed")
        
        return inserted_id
        
    except DatabaseError as db_e:
        logger.error(f"Database error during webmethods insert: {db_e}")
        try:
            conn.rollback()
            logger.info("WebMethods database transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback webmethods transaction: {rollback_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during webmethods insert: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        try:
            conn.rollback()
            logger.info("WebMethods database transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback webmethods transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("WebMethods database cursor closed")
            except Exception as close_e:
                logger.warning(f"Error closing webmethods cursor: {close_e}")

def update_box_link(conn, row_id, box_link):
    """Update box link in database with error handling"""
    cur = None
    try:
        logger.info(f"Updating box link for row ID: {row_id}")
        logger.debug(f"Box link: {box_link}")
        
        cur = conn.cursor()
        logger.debug("Database cursor created for update")
        
        # Execute update query
        cur.execute("UPDATE query_links SET box_link = %s WHERE id = %s", (box_link, row_id))
        logger.debug("Update query executed")
        
        # Check if any row was updated
        if cur.rowcount == 0:
            logger.warning(f"No rows updated for ID: {row_id}")
            logger.warning("This might indicate the ID doesn't exist")
        else:
            logger.info(f"Successfully updated {cur.rowcount} row(s) for ID: {row_id}")
        
        # Commit the transaction
        conn.commit()
        logger.debug("Database update transaction committed")
        
    except DatabaseError as db_e:
        logger.error(f"Database error during update: {db_e}")
        try:
            conn.rollback()
            logger.info("Database update transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback update transaction: {rollback_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during update: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        try:
            conn.rollback()
            logger.info("Database update transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback update transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database update cursor closed")
            except Exception as close_e:
                logger.warning(f"Error closing update cursor: {close_e}")

def get_all_records(conn, limit=None, offset=0):
    """Get all records from query_links table with pagination - handles both old and new schema"""
    cur = None
    try:
        logger.info(f"Fetching records from database with limit: {limit}, offset: {offset}")
        
        cur = conn.cursor()
        
        # First, check what columns exist in the table
        cur.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'query_links' 
            ORDER BY ordinal_position
        """)
        columns = [row[0] for row in cur.fetchall()]
        logger.debug(f"Available columns: {columns}")
        
        # Build query based on available columns
        if 'date' in columns and 'issue' in columns:
            # New schema
            if limit:
                query = """
                    SELECT id, 
                           COALESCE(date, '') as date,
                           COALESCE(issue, '') as issue, 
                           COALESCE(description, '') as description, 
                           COALESCE(metadata, '') as metadata, 
                           box_link, 
                           COALESCE(compiled, issue) as compiled, 
                           created_at, 
                           COALESCE(source, 'unknown') as source
                    FROM query_links ORDER BY id DESC LIMIT %s OFFSET %s
                """
                cur.execute(query, (limit, offset))
            else:
                query = """
                    SELECT id, 
                           COALESCE(date, '') as date,
                           COALESCE(issue, '') as issue, 
                           COALESCE(description, '') as description, 
                           COALESCE(metadata, '') as metadata, 
                           box_link, 
                           COALESCE(compiled, issue) as compiled, 
                           created_at, 
                           COALESCE(source, 'unknown') as source
                    FROM query_links ORDER BY id DESC
                """
                cur.execute(query)
        else:
            # Old schema fallback
            if limit:
                query = """
                    SELECT id, 
                           '' as date,
                           COALESCE(queries, '') as issue, 
                           '' as description, 
                           '' as metadata, 
                           box_link, 
                           COALESCE(queries, '') as compiled, 
                           CURRENT_TIMESTAMP as created_at, 
                           'legacy' as source
                    FROM query_links ORDER BY id DESC LIMIT %s OFFSET %s
                """
                cur.execute(query, (limit, offset))
            else:
                query = """
                    SELECT id, 
                           '' as date,
                           COALESCE(queries, '') as issue, 
                           '' as description, 
                           '' as metadata, 
                           box_link, 
                           COALESCE(queries, '') as compiled, 
                           CURRENT_TIMESTAMP as created_at, 
                           'legacy' as source
                    FROM query_links ORDER BY id DESC
                """
                cur.execute(query)
        
        records = cur.fetchall()
        logger.info(f"Retrieved {len(records)} records from database")
        
        # Convert to list of dictionaries
        result = []
        for record in records:
            result.append({
                'id': record[0],
                'date': record[1] if record[1] else '',
                'issue': record[2] if record[2] else '',
                'description': record[3] if record[3] else '',
                'metadata': record[4] if record[4] else '',
                'box_link': record[5],
                'compiled': record[6] if record[6] else '',
                'created_at': record[7].isoformat() if record[7] else None,
                'source': record[8] if record[8] else 'unknown',
                # Keep queries field for backwards compatibility with frontend
                'queries': record[6] if record[6] else record[2] if record[2] else ''
            })
        
        return result
        
    except DatabaseError as db_e:
        logger.error(f"Database error during record retrieval: {db_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during record retrieval: {e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after record retrieval")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after record retrieval: {close_e}")

def get_record_count(conn):
    """Get total count of records in query_links table"""
    cur = None
    try:
        logger.debug("Getting total record count")
        
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM query_links")
        count = cur.fetchone()[0]
        
        logger.info(f"Total records count: {count}")
        return count
        
    except DatabaseError as db_e:
        logger.error(f"Database error during count retrieval: {db_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during count retrieval: {e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after count retrieval")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after count retrieval: {close_e}")

def update_record(conn, record_id, queries, box_link=None):
    """Update a record in the database - supports both old and new schema"""
    cur = None
    try:
        logger.info(f"Updating record ID: {record_id}")
        logger.debug(f"New queries: {queries[:100]}...")
        
        cur = conn.cursor()
        
        # Check what columns exist in the table
        cur.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'query_links' 
            ORDER BY ordinal_position
        """)
        columns = [row[0] for row in cur.fetchall()]
        has_issue_column = 'issue' in columns
        
        # Update based on available schema
        if has_issue_column:
            # New schema - update issue field
            if box_link is not None:
                cur.execute(
                    "UPDATE query_links SET issue = %s, box_link = %s WHERE id = %s",
                    (queries, box_link, record_id)
                )
            else:
                cur.execute(
                    "UPDATE query_links SET issue = %s WHERE id = %s",
                    (queries, record_id)
                )
        else:
            # Old schema - update queries field
            if box_link is not None:
                cur.execute(
                    "UPDATE query_links SET queries = %s, box_link = %s WHERE id = %s",
                    (queries, box_link, record_id)
                )
            else:
                cur.execute(
                    "UPDATE query_links SET queries = %s WHERE id = %s",
                    (queries, record_id)
                )
        
        if cur.rowcount == 0:
            logger.warning(f"No record found with ID: {record_id}")
            return False
        
        conn.commit()
        logger.info(f"Successfully updated record ID: {record_id}")
        return True
        
    except DatabaseError as db_e:
        logger.error(f"Database error during record update: {db_e}")
        try:
            conn.rollback()
        except Exception as rollback_e:
            logger.error(f"Failed to rollback update transaction: {rollback_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during record update: {e}")
        try:
            conn.rollback()
        except Exception as rollback_e:
            logger.error(f"Failed to rollback update transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after record update")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after record update: {close_e}")

def delete_record(conn, record_id):
    """Delete a record from the database"""
    cur = None
    try:
        logger.info(f"Deleting record ID: {record_id}")
        
        cur = conn.cursor()
        cur.execute("DELETE FROM query_links WHERE id = %s", (record_id,))
        
        if cur.rowcount == 0:
            logger.warning(f"No record found with ID: {record_id}")
            return False
        
        conn.commit()
        logger.info(f"Successfully deleted record ID: {record_id}")
        return True
        
    except DatabaseError as db_e:
        logger.error(f"Database error during record deletion: {db_e}")
        try:
            conn.rollback()
        except Exception as rollback_e:
            logger.error(f"Failed to rollback delete transaction: {rollback_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during record deletion: {e}")
        try:
            conn.rollback()
        except Exception as rollback_e:
            logger.error(f"Failed to rollback delete transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after record deletion")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after record deletion: {close_e}")

def get_dashboard_analytics(conn):
    """Get comprehensive dashboard analytics - handles both old and new schema"""
    cur = None
    try:
        logger.debug("Getting dashboard analytics")
        
        cur = conn.cursor()
        
        # Check what columns exist in the table
        cur.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'query_links' 
            ORDER BY ordinal_position
        """)
        columns = [row[0] for row in cur.fetchall()]
        has_new_schema = 'source' in columns and 'issue' in columns
        
        # Get basic counts
        cur.execute("SELECT COUNT(*) FROM query_links")
        total_records = cur.fetchone()[0]
        
        cur.execute("SELECT COUNT(*) FROM query_links WHERE box_link IS NOT NULL AND box_link != ''")
        records_with_links = cur.fetchone()[0]
        
        # Get recent activity (last 7 days)
        cur.execute("""
            SELECT COUNT(*) FROM query_links 
            WHERE id > (SELECT COALESCE(MAX(id), 0) - 100 FROM query_links)
        """)
        recent_records = cur.fetchone()[0]
        
        # Get data source breakdown (adapted for both schemas)
        if has_new_schema:
            cur.execute("""
                SELECT 
                    CASE 
                        WHEN source = 'webmethods' THEN 'WebMethods'
                        WHEN source = 'legacy' THEN 'Legacy'
                        WHEN issue LIKE 'Issue Report%' THEN 'Issues'
                        WHEN issue LIKE '%requirements%' OR issue LIKE '%extraction%' THEN 'Extractions'
                        ELSE 'Other'
                    END as source_type,
                    COUNT(*) as count
                FROM query_links 
                GROUP BY 
                    CASE 
                        WHEN source = 'webmethods' THEN 'WebMethods'
                        WHEN source = 'legacy' THEN 'Legacy'
                        WHEN issue LIKE 'Issue Report%' THEN 'Issues'
                        WHEN issue LIKE '%requirements%' OR issue LIKE '%extraction%' THEN 'Extractions'
                        ELSE 'Other'
                    END
                ORDER BY count DESC
            """)
        else:
            # Fallback for old schema
            cur.execute("""
                SELECT 
                    CASE 
                        WHEN queries LIKE 'Issue Report%' THEN 'Issues'
                        WHEN queries LIKE '%requirements%' OR queries LIKE '%extraction%' THEN 'Extractions'
                        ELSE 'Legacy'
                    END as source_type,
                    COUNT(*) as count
                FROM query_links 
                GROUP BY 
                    CASE 
                        WHEN queries LIKE 'Issue Report%' THEN 'Issues'
                        WHEN queries LIKE '%requirements%' OR queries LIKE '%extraction%' THEN 'Extractions'
                        ELSE 'Legacy'
                    END
                ORDER BY count DESC
            """)
        
        source_breakdown = {}
        for row in cur.fetchall():
            source_breakdown[row[0]] = row[1]
        
        # Get top issues by length (most detailed)
        if has_new_schema:
            cur.execute("""
                SELECT id, COALESCE(LENGTH(compiled), LENGTH(issue), 0) as content_length
                FROM query_links 
                ORDER BY content_length DESC 
                LIMIT 5
            """)
        else:
            cur.execute("""
                SELECT id, COALESCE(LENGTH(queries), 0) as content_length
                FROM query_links 
                ORDER BY content_length DESC 
                LIMIT 5
            """)
        top_detailed_records = [{"id": row[0], "length": row[1]} for row in cur.fetchall()]
        
        # Get completion rate (records with box links)
        completion_rate = (records_with_links / total_records * 100) if total_records > 0 else 0
        
        analytics = {
            "summary": {
                "total_records": total_records,
                "records_with_links": records_with_links,
                "records_without_links": total_records - records_with_links,
                "completion_rate": round(completion_rate, 1),
                "recent_activity": recent_records
            },
            "source_breakdown": source_breakdown,
            "top_detailed_records": top_detailed_records,
            "trends": {
                "completion_trend": "stable",  # Could be enhanced with time-series data
                "activity_level": "moderate" if recent_records < 20 else "high"
            }
        }
        
        logger.info(f"Analytics calculated: {total_records} total records, {completion_rate:.1f}% completion rate")
        return analytics
        
    except DatabaseError as db_e:
        logger.error(f"Database error during analytics calculation: {db_e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during analytics calculation: {e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after analytics calculation")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after analytics calculation: {close_e}")

def log_activity(conn, action, record_id=None, user_ip=None, details=None):
    """Log user activity for audit trail"""
    cur = None
    try:
        logger.debug(f"Logging activity: {action}")
        
        cur = conn.cursor()
        
        # Create activity_log table if it doesn't exist
        cur.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                action VARCHAR(100) NOT NULL,
                record_id INTEGER,
                user_ip VARCHAR(45),
                details TEXT,
                success BOOLEAN DEFAULT TRUE
            )
        """)
        
        # Insert activity log
        cur.execute("""
            INSERT INTO activity_log (action, record_id, user_ip, details)
            VALUES (%s, %s, %s, %s)
        """, (action, record_id, user_ip, details))
        
        conn.commit()
        logger.debug(f"Activity logged successfully: {action}")
        
    except Exception as e:
        logger.error(f"Failed to log activity: {e}")
        # Don't raise exception for logging failures
    finally:
        if cur:
            try:
                cur.close()
            except Exception as close_e:
                logger.warning(f"Error closing cursor after activity logging: {close_e}")

def get_recent_activities(conn, limit=50):
    """Get recent user activities"""
    cur = None
    try:
        logger.debug("Getting recent activities")
        
        cur = conn.cursor()
        
        # Check if activity_log table exists
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'activity_log'
            )
        """)
        
        if not cur.fetchone()[0]:
            return []
        
        cur.execute("""
            SELECT timestamp, action, record_id, user_ip, details, success
            FROM activity_log 
            ORDER BY timestamp DESC 
            LIMIT %s
        """, (limit,))
        
        activities = []
        for row in cur.fetchall():
            activities.append({
                "timestamp": row[0].isoformat() if row[0] else None,
                "action": row[1],
                "record_id": row[2],
                "user_ip": row[3],
                "details": row[4],
                "success": row[5]
            })
        
        logger.info(f"Retrieved {len(activities)} recent activities")
        return activities
        
    except Exception as e:
        logger.error(f"Error getting recent activities: {e}")
        return []
    finally:
        if cur:
            try:
                cur.close()
            except Exception as close_e:
                logger.warning(f"Error closing cursor after getting activities: {close_e}")

def reset_database(conn):
    """Reset the database to initial state - WARNING: This deletes all data"""
    cur = None
    try:
        logger.warning("Database reset requested - This will delete ALL data!")
        
        cur = conn.cursor()
        
        # Get list of tables to reset
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            AND table_name IN ('query_links', 'activity_log')
        """)
        tables = [row[0] for row in cur.fetchall()]
        
        reset_info = {
            "tables_found": tables,
            "records_deleted": {}
        }
        
        # Count records before deletion
        for table in tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table}")
                count = cur.fetchone()[0]
                reset_info["records_deleted"][table] = count
                logger.info(f"Table {table} has {count} records to be deleted")
            except Exception as count_e:
                logger.warning(f"Could not count records in {table}: {count_e}")
                reset_info["records_deleted"][table] = "unknown"
        
        # Delete all data from tables
        for table in tables:
            try:
                # Use TRUNCATE for faster deletion and auto-reset of sequences
                cur.execute(f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE")
                logger.info(f"Successfully truncated table: {table}")
            except Exception as truncate_e:
                logger.warning(f"TRUNCATE failed for {table}, trying DELETE: {truncate_e}")
                try:
                    # Fallback to DELETE if TRUNCATE fails
                    cur.execute(f"DELETE FROM {table}")
                    logger.info(f"Successfully deleted all records from: {table}")
                except Exception as delete_e:
                    logger.error(f"Failed to delete from {table}: {delete_e}")
                    raise
        
        # Reset sequences manually if needed
        try:
            cur.execute("ALTER SEQUENCE query_links_id_seq RESTART WITH 1")
            logger.info("Reset query_links ID sequence")
        except Exception as seq_e:
            logger.warning(f"Could not reset query_links sequence: {seq_e}")
            
        try:
            cur.execute("ALTER SEQUENCE activity_log_id_seq RESTART WITH 1")
            logger.info("Reset activity_log ID sequence")
        except Exception as seq_e:
            logger.warning(f"Could not reset activity_log sequence: {seq_e}")
        
        # Commit all changes
        conn.commit()
        
        # Log the reset action
        try:
            cur.execute("""
                INSERT INTO activity_log (action, details, success)
                VALUES (%s, %s, %s)
            """, (
                "database_reset", 
                f"Database reset completed. Deleted records: {reset_info['records_deleted']}", 
                True
            ))
            conn.commit()
            logger.info("Reset action logged to activity_log")
        except Exception as log_e:
            logger.warning(f"Could not log reset action: {log_e}")
        
        logger.warning("Database reset completed successfully")
        return reset_info
        
    except Exception as e:
        logger.error(f"Error during database reset: {e}")
        try:
            conn.rollback()
            logger.info("Database reset transaction rolled back")
        except Exception as rollback_e:
            logger.error(f"Failed to rollback reset transaction: {rollback_e}")
        raise
    finally:
        if cur:
            try:
                cur.close()
                logger.debug("Database cursor closed after reset")
            except Exception as close_e:
                logger.warning(f"Error closing cursor after reset: {close_e}")
