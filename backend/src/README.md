# Requirements Extraction Pipeline

A comprehensive Python package for extracting "要求事項" (requirements) from Japanese construction specification documents using both table detection and LLM-based approaches with WatsonX integration.

## Features

- **Dual-Route Extraction**: Automatically tries table detection first, falls back to LLM if needed
- **Multi-Method Table Detection**: Uses Camelot, Tabula, and PDFPlumber for robust table detection
- **WatsonX LLM Integration**: Leverages IBM WatsonX for intelligent text extraction
- **Japanese Language Optimized**: Specialized prompts and processing for Japanese construction documents
- **Confidence Scoring**: Provides reliability assessment for extraction results
- **Structured JSON Output**: Clean, consistent output format with metadata
- **Error Handling & Retry Logic**: Production-ready reliability features

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd requirements_extraction

# Install dependencies
pip install -r requirements.txt

# For camelot dependencies (may require system packages)
# Ubuntu/Debian:
sudo apt-get install python3-dev python3-tk ghostscript
# macOS:
brew install ghostscript
```

## Quick Start

### 1. Environment Setup

```bash
# Set environment variables
export WATSONX_API_KEY="your_api_key_here"
export WATSONX_PROJECT_ID="your_project_id_here"
```

### 2. Basic Usage

```python
from requirements_extraction import Config, RequirementsExtractionPipeline

# Create configuration
config = Config.from_env()  # Uses environment variables

# Initialize pipeline
pipeline = RequirementsExtractionPipeline(config)

# Extract requirements
result = pipeline.extract_requirements("document.pdf")

# Export to JSON
json_output = pipeline.export_to_json(result, "output.json")

print(f"Success: {result.success}")
print(f"Requirements found: {result.data.get('total_requirements', 0)}")
```

### 3. Command Line Usage

```bash
# Basic extraction
python -m requirements_extraction document.pdf -o output.json

# With custom parameters
python -m requirements_extraction document.pdf \
    --api-key YOUR_API_KEY \
    --project-id YOUR_PROJECT_ID \
    --model-id meta-llama/llama-3-70b-instruct \
    --temperature 0.1 \
    --output results.json
```

## Configuration

### Environment Variables

```bash
WATSONX_API_KEY=your_api_key
WATSONX_PROJECT_ID=your_project_id
WATSONX_MODEL_ID=meta-llama/llama-3-70b-instruct  # Optional
WATSONX_BASE_URL=https://us-south.ml.cloud.ibm.com  # Optional
WATSONX_MAX_TOKENS=4000  # Optional
WATSONX_TEMPERATURE=0.1  # Optional
WATSONX_TIMEOUT=300  # Optional
```

### Programmatic Configuration

```python
from requirements_extraction.config import Config, WatsonXConfig

# Custom configuration
config = Config(
    watsonx=WatsonXConfig(
        api_key="your_api_key",
        project_id="your_project_id",
        model_id="meta-llama/llama-3-70b-instruct",
        temperature=0.1,
        max_tokens=4000
    )
)

pipeline = RequirementsExtractionPipeline(config)
```

## Output Format

```json
{
  "extraction_metadata": {
    "method_used": "table_extraction",
    "success": true,
    "confidence_score": 0.85,
    "error_message": null,
    "extraction_timestamp": **********.0
  },
  "extracted_data": {
    "total_requirements": 15,
    "requirements": [
      {
        "requirement": "発電所構内におけるルール「堺港発電所　入構者心得」を遵守すること。",
        "witness": "",
        "item": "（１）",
        "details": null,
        "source_page": 4,
        "extraction_method": "camelot",
        "confidence": 0.92
      }
    ],
    "summary": {
      "total_count": 15,
      "by_page": {"4": 10, "5": 5},
      "by_method": {"camelot": 12, "tabula": 3},
      "average_confidence": 0.87
    }
  }
}
```

## Pipeline Architecture

```
PDF Input
    ↓
┌─────────────────┐
│ Table Detection │ (Camelot, Tabula, PDFPlumber)
└─────────────────┘
    ↓
Has Tables? ──NO──→ ┌─────────────────┐
    │               │ LLM Extraction  │ (WatsonX)
    YES               └─────────────────┘
    ↓                          │
┌─────────────────┐             │
│ Table Extraction│             │
└─────────────────┘             │
    ↓                          │
Confidence > 0.6? ──NO──────────┘
    │
    YES
    ↓
┌─────────────────┐
│ JSON Output     │
└─────────────────┘
```

## Advanced Usage

### Custom Table Detection

```python
from requirements_extraction.table_detector import TableDetector
from requirements_extraction.config import TableDetectionConfig

# Custom table detection configuration
table_config = TableDetectionConfig(
    japanese_table_keywords=["要求事項", "立会項目", "作業内容"],
    min_confidence_threshold=0.7,
    min_keyword_matches=3
)

detector = TableDetector(table_config)
tables = detector.detect_tables_in_pdf("document.pdf")
```

### LLM-Only Extraction

```python
from requirements_extraction.llm_extractor import LLMExtractor
from requirements_extraction.watsonx_client import WatsonXLLM
from requirements_extraction.config import WatsonXConfig

# Initialize LLM extractor
config = WatsonXConfig(api_key="...", project_id="...")
llm = WatsonXLLM(config)
extractor = LLMExtractor(llm)

# Extract from text
document_text = "..."  # Your document text
result = extractor.extract_requirements_with_llm(document_text)
```

### Batch Processing

```python
import glob
from pathlib import Path

# Process multiple files
pdf_files = glob.glob("documents/*.pdf")
results = []

for pdf_path in pdf_files:
    try:
        result = pipeline.extract_requirements(pdf_path)
        results.append({
            'file': Path(pdf_path).name,
            'result': result.to_dict()
        })
    except Exception as e:
        print(f"Error processing {pdf_path}: {e}")

# Save batch results
import json
with open("batch_results.json", "w", encoding="utf-8") as f:
    json.dump(results, f, ensure_ascii=False, indent=2)
```

## Error Handling

The pipeline includes comprehensive error handling:

- **Connection errors**: Automatic retry with exponential backoff
- **Table detection failures**: Graceful fallback to LLM
- **Parse errors**: Fallback data structures
- **API timeouts**: Configurable timeout settings

```python
try:
    result = pipeline.extract_requirements("document.pdf")
    if not result.success:
        print(f"Extraction failed: {result.error_message}")
except Exception as e:
    print(f"Pipeline error: {e}")
```

## Performance Optimization

### Tips for Better Results

1. **PDF Quality**: Ensure PDFs are text-searchable (not scanned images)
2. **Table Structure**: Clear table formatting improves detection
3. **Document Length**: Very long documents are automatically truncated
4. **Language Model**: Choose appropriate WatsonX model for your needs

### Monitoring

```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Check pipeline info
info = pipeline.get_pipeline_info()
print(f"Using model: {info['watsonx_model']}")

# Test connections
if pipeline.test_connection():
    print("All systems ready")
```

## Troubleshooting

### Common Issues

1. **No tables detected**:
   - Check if PDF contains actual tables
   - Verify table formatting is clear
   - Pipeline will automatically use LLM fallback

2. **Low confidence scores**:
   - Improve PDF quality
   - Adjust confidence thresholds
   - Check Japanese text encoding

3. **WatsonX API errors**:
   - Verify API credentials
   - Check project permissions
   - Monitor rate limits

4. **Installation issues**:
   ```bash
   # For Camelot issues
   pip install camelot-py[cv] --force-reinstall
   
   # For Japanese text issues
   pip install japanize-matplotlib
   ```

### Debug Mode

```bash
# Run with debug logging
python -m requirements_extraction document.pdf --log-level DEBUG

# Test individual components
python -c "
from requirements_extraction.table_detector import TableDetector
detector = TableDetector()
tables = detector.detect_tables_in_pdf('document.pdf')
print(f'Found {len(tables)} tables')
"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review logs with debug logging enabled
- Open an issue with detailed error information