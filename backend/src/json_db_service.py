import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# Configure logging for this module
logger = logging.getLogger(__name__)

# Database file path
DB_FILE_PATH = os.path.join(os.path.dirname(__file__), '..', 'data', 'database.json')

class JSONDatabase:
    """JSON file-based database for simple operations"""
    
    def __init__(self):
        self.db_path = DB_FILE_PATH
        self._ensure_db_file()
    
    def _ensure_db_file(self):
        """Ensure database file and directory exist"""
        try:
            # Create data directory if it doesn't exist
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            
            # Create database file if it doesn't exist
            if not os.path.exists(self.db_path):
                initial_data = {
                    "query_links": [],
                    "activities": [],
                    "next_id": 1
                }
                self._write_data(initial_data)
                logger.info("Created new JSON database file")
            
            logger.info(f"JSON database file ready at: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Failed to ensure database file: {e}")
            raise
    
    def _read_data(self) -> Dict[str, Any]:
        """Read data from JSON file"""
        try:
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logger.error(f"Failed to read database file: {e}")
            # Return empty structure if file is corrupted
            return {"query_links": [], "activities": [], "next_id": 1}
    
    def _write_data(self, data: Dict[str, Any]):
        """Write data to JSON file"""
        try:
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to write database file: {e}")
            raise
    
    def _get_next_id(self, data: Dict[str, Any]) -> int:
        """Get next available ID"""
        current_id = data.get("next_id", 1)
        data["next_id"] = current_id + 1
        return current_id

# Global database instance
db = JSONDatabase()

def get_connection():
    """Get database connection (returns the database instance for compatibility)"""
    logger.info("JSON database connection established")
    return db

def get_all_records(conn, limit: int = 1000) -> List[Dict[str, Any]]:
    """Get all records from query_links"""
    try:
        logger.info(f"Retrieving all records with limit: {limit}")
        data = conn._read_data()
        records = data.get("query_links", [])
        
        # Sort by id descending (newest first)
        records = sorted(records, key=lambda x: x.get("id", 0), reverse=True)
        
        # Apply limit
        if limit:
            records = records[:limit]
        
        logger.info(f"Retrieved {len(records)} records")
        return records
        
    except Exception as e:
        logger.error(f"Error retrieving all records: {e}")
        return []

def get_record_count(conn) -> int:
    """Get total count of records"""
    try:
        data = conn._read_data()
        count = len(data.get("query_links", []))
        logger.info(f"Total record count: {count}")
        return count
        
    except Exception as e:
        logger.error(f"Error getting record count: {e}")
        return 0

def insert_webmethods_data(conn, date: str, issue: str, description: str, metadata: str, box_link: str) -> int:
    """Insert webmethods data with separate fields"""
    try:
        logger.info("Inserting webmethods data to JSON database")
        
        data = conn._read_data()
        record_id = conn._get_next_id(data)
        
        new_record = {
            "id": record_id,
            "date": date,
            "issue": issue,
            "description": description,
            "metadata": metadata,
            "queries": None,  # Will be populated later if needed
            "box_link": box_link,
            "source": "webmethods",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        data["query_links"].append(new_record)
        conn._write_data(data)
        
        logger.info(f"WebMethods data inserted with ID: {record_id}")
        
        # Log activity
        log_activity(conn, "webmethods_data_inserted", f"New record ID: {record_id}", record_id)
        
        return record_id
        
    except Exception as e:
        logger.error(f"Error inserting webmethods data: {e}")
        raise

def update_record(conn, record_id: int, issue: str = None, description: str = None, box_link: str = None) -> bool:
    """Update a record with issue and description"""
    try:
        logger.info(f"Updating record ID: {record_id}")
        
        data = conn._read_data()
        records = data.get("query_links", [])
        
        # Find and update the record
        for record in records:
            if record.get("id") == record_id:
                if issue is not None:
                    record["issue"] = issue
                if description is not None:
                    record["description"] = description
                if box_link is not None:
                    record["box_link"] = box_link
                record["updated_at"] = datetime.now().isoformat()
                
                conn._write_data(data)
                logger.info(f"Record {record_id} updated successfully")
                
                # Log activity
                log_activity(conn, "record_updated", f"Record ID: {record_id}", record_id)
                
                return True
        
        logger.warning(f"Record {record_id} not found for update")
        return False
        
    except Exception as e:
        logger.error(f"Error updating record {record_id}: {e}")
        return False

def delete_record(conn, record_id: int) -> bool:
    """Delete a record"""
    try:
        logger.info(f"Deleting record ID: {record_id}")
        
        data = conn._read_data()
        records = data.get("query_links", [])
        
        # Find and remove the record
        for i, record in enumerate(records):
            if record.get("id") == record_id:
                del records[i]
                conn._write_data(data)
                logger.info(f"Record {record_id} deleted successfully")
                
                # Log activity
                log_activity(conn, "record_deleted", f"Record ID: {record_id}", record_id)
                
                return True
        
        logger.warning(f"Record {record_id} not found for deletion")
        return False
        
    except Exception as e:
        logger.error(f"Error deleting record {record_id}: {e}")
        return False

def get_dashboard_analytics(conn) -> Dict[str, Any]:
    """Get dashboard analytics"""
    try:
        logger.info("Calculating dashboard analytics")
        
        data = conn._read_data()
        records = data.get("query_links", [])
        
        total_records = len(records)
        records_with_links = len([r for r in records if r.get("box_link")])
        records_without_links = total_records - records_with_links
        
        # Count by source
        source_counts = {}
        for record in records:
            source = record.get("source", "unknown")
            source_counts[source] = source_counts.get(source, 0) + 1
        
        # Recent activity (last 7 days)
        from datetime import datetime, timedelta
        seven_days_ago = datetime.now() - timedelta(days=7)
        recent_records = 0
        
        for record in records:
            try:
                created_at = datetime.fromisoformat(record.get("created_at", "").replace("Z", "+00:00"))
                if created_at.replace(tzinfo=None) > seven_days_ago:
                    recent_records += 1
            except:
                pass
        
        completion_rate = round((records_with_links / total_records * 100) if total_records > 0 else 0, 1)
        
        # Determine trends
        activity_level = "High" if recent_records > 5 else "Medium" if recent_records > 2 else "Low"
        completion_trend = "Increasing" if completion_rate > 75 else "Stable" if completion_rate > 50 else "Needs Attention"
        
        analytics = {
            "summary": {
                "total_records": total_records,
                "records_with_links": records_with_links,
                "records_without_links": records_without_links,
                "completion_rate": completion_rate,
                "recent_activity": recent_records
            },
            "trends": {
                "completion_trend": completion_trend,
                "activity_level": activity_level
            },
            "source_breakdown": source_counts,
            "last_updated": datetime.now().isoformat()
        }
        
        logger.info("Dashboard analytics calculated successfully")
        return analytics
        
    except Exception as e:
        logger.error(f"Error calculating analytics: {e}")
        return {
            "summary": {
                "total_records": 0,
                "records_with_links": 0,
                "records_without_links": 0,
                "completion_rate": 0,
                "recent_activity": 0
            },
            "trends": {
                "completion_trend": "Unknown",
                "activity_level": "Low"
            },
            "source_breakdown": {},
            "last_updated": datetime.now().isoformat()
        }

def log_activity(conn, activity_type: str, details: str = None, record_id: int = None):
    """Log an activity with proper formatting"""
    try:
        logger.debug(f"Logging activity: {activity_type}")
        
        data = conn._read_data()
        activity_id = conn._get_next_id(data)
        
        # Create timestamp in proper format
        timestamp = datetime.now().isoformat()
        
        new_activity = {
            "id": activity_id,
            "activity_type": activity_type,
            "action": activity_type,  # Add action field for frontend compatibility
            "details": details,
            "record_id": record_id,  # Include record_id if provided
            "created_at": timestamp,
            "timestamp": timestamp  # Add timestamp field for frontend compatibility
        }
        
        if "activities" not in data:
            data["activities"] = []
        
        data["activities"].append(new_activity)
        
        # Keep only last 100 activities
        if len(data["activities"]) > 100:
            data["activities"] = data["activities"][-100:]
        
        conn._write_data(data)
        logger.debug(f"Activity logged: {activity_type}")
        
    except Exception as e:
        logger.error(f"Error logging activity: {e}")

def get_recent_activities(conn, limit: int = 50) -> List[Dict[str, Any]]:
    """Get recent activities"""
    try:
        logger.info(f"Retrieving recent activities with limit: {limit}")
        
        data = conn._read_data()
        activities = data.get("activities", [])
        
        # Convert and ensure proper fields for frontend compatibility
        for activity in activities:
            # Ensure action field exists
            if 'activity_type' in activity and 'action' not in activity:
                activity['action'] = activity['activity_type']
            
            # Ensure timestamp field exists
            if 'created_at' in activity and 'timestamp' not in activity:
                activity['timestamp'] = activity['created_at']
            
            # Ensure success field exists (default to True for successful activities)
            if 'success' not in activity:
                activity['success'] = True
        
        # Sort by created_at descending (newest first)
        activities = sorted(activities, key=lambda x: x.get("created_at", ""), reverse=True)
        
        # Apply limit
        if limit:
            activities = activities[:limit]
        
        logger.info(f"Retrieved {len(activities)} activities")
        return activities
        
    except Exception as e:
        logger.error(f"Error retrieving activities: {e}")
        return []

def reset_database(conn) -> Dict[str, str]:
    """Reset database to initial state"""
    try:
        logger.warning("Resetting JSON database")
        
        initial_data = {
            "query_links": [],
            "activities": [{
                "id": 1,
                "activity_type": "database_reset",
                "details": "Database reset to initial state",
                "created_at": datetime.now().isoformat()
            }],
            "next_id": 2
        }
        
        conn._write_data(initial_data)
        
        reset_info = {
            "reset_time": datetime.now().isoformat(),
            "records_deleted": "all",
            "status": "success"
        }
        
        logger.warning("JSON database reset completed")
        return reset_info
        
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        raise

# Legacy support functions
def insert_query_get_id(conn, query: str, box_link: str = None) -> int:
    """Insert query into database and return the ID (legacy support)"""
    return insert_webmethods_data(conn, 
                                  datetime.now().strftime("%Y-%m-%d"),
                                  query,
                                  "Legacy query insertion",
                                  "Legacy metadata",
                                  box_link)