"""
Configuration settings for the requirements extraction pipeline.
"""

import os
import yaml
import logging
from dataclasses import dataclass
from typing import List, Optional

# Configure logging for this module
logger = logging.getLogger(__name__)


@dataclass
class WatsonXConfig:
    """Configuration for WatsonX API"""
    api_key: str
    project_id: str
    ibm_cloud_url: str = None
    model_id: str = "meta-llama/llama-3-3-70b-instruct"
    base_url: str = "https://us-south.ml.cloud.ibm.com"  # Kept for backward compatibility
    max_tokens: int = 8192
    temperature: float = 0.3
    timeout: int = 300
    
    def __post_init__(self):
        # Set IBM Cloud URL to base_url if not provided
        if self.ibm_cloud_url is None:
            self.ibm_cloud_url = self.base_url
    
    @classmethod
    def from_env(cls) -> 'WatsonXConfig':
        """Create configuration from environment variables with validation"""
        try:
            logger.info("Loading WatsonX configuration from environment variables")
            
            # Get required variables
            api_key = os.getenv('WATSONX_API_KEY', '')
            project_id = os.getenv('WATSONX_PROJECT_ID', '')
            
            # Log configuration status (without secrets)
            logger.info(f"WatsonX config status:")
            logger.info(f"  API_KEY: {'[SET]' if api_key else '[NOT SET]'}")
            logger.info(f"  PROJECT_ID: {'[SET]' if project_id else '[NOT SET]'}")
            
            # Validate required fields
            if not api_key:
                logger.warning("WATSONX_API_KEY not set - WatsonX features will be unavailable")
            if not project_id:
                logger.warning("WATSONX_PROJECT_ID not set - WatsonX features will be unavailable")
                
            # Get optional variables with error handling
            try:
                max_tokens = int(os.getenv('WATSONX_MAX_TOKENS', '8192'))
                logger.debug(f"Max tokens: {max_tokens}")
            except ValueError as e:
                logger.warning(f"Invalid WATSONX_MAX_TOKENS value, using default: {e}")
                max_tokens = 8192
                
            try:
                temperature = float(os.getenv('WATSONX_TEMPERATURE', '0.3'))
                logger.debug(f"Temperature: {temperature}")
            except ValueError as e:
                logger.warning(f"Invalid WATSONX_TEMPERATURE value, using default: {e}")
                temperature = 0.3
                
            try:
                timeout = int(os.getenv('WATSONX_TIMEOUT', '300'))
                logger.debug(f"Timeout: {timeout}")
            except ValueError as e:
                logger.warning(f"Invalid WATSONX_TIMEOUT value, using default: {e}")
                timeout = 300
            
            # Get URLs
            ibm_cloud_url = os.getenv('IBM_CLOUD_URL', 'https://us-south.ml.cloud.ibm.com')
            model_id = os.getenv('WATSONX_MODEL_ID', 'meta-llama/llama-3-3-70b-instruct')
            base_url = os.getenv('WATSONX_BASE_URL', 'https://us-south.ml.cloud.ibm.com')
            
            logger.info(f"  MODEL_ID: {model_id}")
            logger.info(f"  IBM_CLOUD_URL: {ibm_cloud_url}")
            
            config = cls(
                api_key=api_key,
                project_id=project_id,
                ibm_cloud_url=ibm_cloud_url,
                model_id=model_id,
                base_url=base_url,
                max_tokens=max_tokens,
                temperature=temperature,
                timeout=timeout
            )
            
            logger.info("WatsonX configuration loaded successfully")
            return config
            
        except Exception as e:
            logger.error(f"Failed to load WatsonX configuration: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            raise


@dataclass
class TableDetectionConfig:
    """Configuration for table detection"""
    japanese_table_keywords: List[str] = None
    min_confidence_threshold: float = 0.5
    min_keyword_matches: int = 2
    
    def __post_init__(self):
        if self.japanese_table_keywords is None:
            self.japanese_table_keywords = [
                "要求事項", "立会項目", "工事範囲", "作業", "点検", 
                "試験", "検査", "項目", "内容", "仕様", "実施"
            ]


@dataclass
class ExtractionConfig:
    """Configuration for extraction pipeline"""
    table_confidence_threshold: float = 0.6
    llm_fallback_enabled: bool = True
    max_retries: int = 3
    enable_validation: bool = True
    export_detailed_logs: bool = True


@dataclass
class DashboardConfig:
    """Configuration for dashboard functionality"""
    passwords: List[str] = None
    items_per_page: int = 50
    auto_refresh_interval: int = 30000  # milliseconds
    enable_real_time_updates: bool = True
    max_displayed_items: int = 1000
    session_timeout: int = 1800000  # milliseconds
    max_login_attempts: int = 3
    lockout_duration: int = 300000  # milliseconds
    theme: str = "professional"
    enable_animations: bool = True
    compact_mode: bool = False
    
    def __post_init__(self):
        if self.passwords is None:
            self.passwords = ["admin123"]  # Default password
    
    @classmethod
    def from_yaml(cls, config_path: str = None) -> 'DashboardConfig':
        """Load dashboard configuration from YAML file"""
        try:
            if config_path is None:
                config_path = os.path.join(os.path.dirname(__file__), '..', 'config.yaml')
            
            logger.info(f"Loading dashboard configuration from: {config_path}")
            
            if not os.path.exists(config_path):
                logger.warning(f"Dashboard config file not found at {config_path}, using defaults")
                return cls()
            
            with open(config_path, 'r', encoding='utf-8') as file:
                yaml_config = yaml.safe_load(file)
            
            if not yaml_config or 'dashboard' not in yaml_config:
                logger.warning("No dashboard configuration found in YAML file, using defaults")
                return cls()
            
            dashboard_config = yaml_config['dashboard']
            
            # Extract configuration values
            passwords = dashboard_config.get('passwords', ["admin123"])
            settings = dashboard_config.get('settings', {})
            security = dashboard_config.get('security', {})
            ui = dashboard_config.get('ui', {})
            
            config = cls(
                passwords=passwords,
                items_per_page=settings.get('items_per_page', 50),
                auto_refresh_interval=settings.get('auto_refresh_interval', 30000),
                enable_real_time_updates=settings.get('enable_real_time_updates', True),
                max_displayed_items=settings.get('max_displayed_items', 1000),
                session_timeout=security.get('session_timeout', 1800000),
                max_login_attempts=security.get('max_login_attempts', 3),
                lockout_duration=security.get('lockout_duration', 300000),
                theme=ui.get('theme', 'professional'),
                enable_animations=ui.get('enable_animations', True),
                compact_mode=ui.get('compact_mode', False)
            )
            
            logger.info(f"Dashboard configuration loaded successfully")
            logger.info(f"  Passwords configured: {len(config.passwords)}")
            logger.info(f"  Items per page: {config.items_per_page}")
            logger.info(f"  Theme: {config.theme}")
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to load dashboard configuration: {e}")
            logger.warning("Using default dashboard configuration")
            return cls()


@dataclass 
class Config:
    """Main configuration class"""
    watsonx: WatsonXConfig
    dashboard: DashboardConfig = None
    table_detection: TableDetectionConfig = None
    extraction: ExtractionConfig = None
    
    def __post_init__(self):
        if self.dashboard is None:
            self.dashboard = DashboardConfig.from_yaml()
        if self.table_detection is None:
            self.table_detection = TableDetectionConfig()
        if self.extraction is None:
            self.extraction = ExtractionConfig()
    
    @classmethod
    def default(cls, api_key: str, project_id: str) -> 'Config':
        """Create default configuration"""
        return cls(
            watsonx=WatsonXConfig(api_key=api_key, project_id=project_id)
        )
    
    @classmethod
    def from_env(cls) -> 'Config':
        """Create configuration from environment variables with comprehensive error handling"""
        try:
            logger.info("Loading main configuration from environment variables")
            
            # Load WatsonX configuration
            watsonx_config = WatsonXConfig.from_env()
            
            # Create main config
            config = cls(watsonx=watsonx_config)
            
            logger.info("Main configuration loaded successfully")
            logger.debug(f"Configuration details:")
            logger.debug(f"  Table detection keywords: {len(config.table_detection.japanese_table_keywords)} items")
            logger.debug(f"  Table confidence threshold: {config.extraction.table_confidence_threshold}")
            logger.debug(f"  LLM fallback enabled: {config.extraction.llm_fallback_enabled}")
            
            return config
            
        except Exception as e:
            logger.error(f"Failed to create configuration from environment: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            raise