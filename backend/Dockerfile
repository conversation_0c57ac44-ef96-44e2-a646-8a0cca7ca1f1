# Backend Dockerfile for Kansai-port FastAPI application
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies for PDF processing and image handling
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    pkg-config \
    ghostscript \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgtkglext1-dev \
    libgtk2.0-dev \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set Java environment for tabula-py
ENV JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64

# Create required directories including data directory for JSON database
RUN mkdir -p /app/logs /app/notebooks /app/UploadedData /app/ExcelData /app/PdfData /app/data

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user for security
RUN groupadd -r appgroup && useradd -r -g appgroup appuser

# Set ownership
RUN chown -R appuser:appgroup /app
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Command to run the application
CMD ["python", "-u", "app.py"]