"""
Utility functions for the requirements extraction pipeline.
"""

import json
import logging
import re
import time
from typing import Dict, List, Any, Optional
import pdfplumber
import pandas as pd

logger = logging.getLogger(__name__)


def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def extract_text_from_pdf(pdf_path: str) -> str:
    """
    Extract text from PDF file
    
    Args:
        pdf_path: Path to PDF file
        
    Returns:
        Extracted text content
    """
    try:
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        
        logger.info(f"Extracted {len(text)} characters from PDF")
        return text
        
    except Exception as e:
        logger.error(f"Failed to extract text from PDF: {str(e)}")
        return ""


def clean_text(text: str) -> str:
    """
    Clean and normalize text content
    
    Args:
        text: Raw text content
        
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    # Remove common PDF artifacts
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    return text


def contains_japanese_keywords(text: str, keywords: List[str]) -> bool:
    """
    Check if text contains Japanese keywords
    
    Args:
        text: Text to check
        keywords: List of keywords to search for
        
    Returns:
        True if keywords found
    """
    if not text or not keywords:
        return False
    
    keyword_count = sum(1 for keyword in keywords if keyword in text)
    return keyword_count >= 2


def calculate_confidence_score(
    base_score: float, 
    requirement_count: int, 
    method_reliability: Dict[str, float]
) -> float:
    """
    Calculate confidence score for extraction
    
    Args:
        base_score: Base confidence score
        requirement_count: Number of requirements found
        method_reliability: Reliability scores by method
        
    Returns:
        Calculated confidence score
    """
    # Boost confidence based on number of requirements
    count_bonus = min(requirement_count * 0.05, 0.2)
    
    # Apply method reliability factor
    method_factor = method_reliability.get('default', 1.0)
    
    final_score = min((base_score + count_bonus) * method_factor, 1.0)
    return round(final_score, 3)


def validate_json_structure(data: Dict[str, Any]) -> bool:
    """
    Validate JSON structure for requirements
    
    Args:
        data: Data to validate
        
    Returns:
        True if valid structure
    """
    try:
        # Check required fields
        if not isinstance(data, dict):
            return False
        
        # Check for requirements array
        requirements = data.get('requirements', [])
        if not isinstance(requirements, list):
            return False
        
        # Validate each requirement
        for req in requirements:
            if not isinstance(req, dict):
                return False
            if 'requirement' not in req:
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"JSON validation error: {str(e)}")
        return False


def export_to_json(
    data: Dict[str, Any], 
    output_path: str = None, 
    ensure_ascii: bool = False
) -> str:
    """
    Export data to JSON format
    
    Args:
        data: Data to export
        output_path: Optional file path to save
        ensure_ascii: Whether to ensure ASCII encoding
        
    Returns:
        JSON string
    """
    try:
        json_str = json.dumps(
            data, 
            ensure_ascii=ensure_ascii, 
            indent=2,
            default=str  # Handle datetime and other objects
        )
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            logger.info(f"Data exported to: {output_path}")
        
        return json_str
        
    except Exception as e:
        logger.error(f"JSON export error: {str(e)}")
        raise


def merge_dataframes(dfs: List[pd.DataFrame]) -> pd.DataFrame:
    """
    Merge multiple dataframes intelligently
    
    Args:
        dfs: List of DataFrames to merge
        
    Returns:
        Merged DataFrame
    """
    if not dfs:
        return pd.DataFrame()
    
    if len(dfs) == 1:
        return dfs[0]
    
    try:
        # Try to concatenate with matching columns
        merged = pd.concat(dfs, ignore_index=True, sort=False)
        return merged
        
    except Exception as e:
        logger.warning(f"DataFrame merge failed: {str(e)}")
        # Fallback: return first non-empty dataframe
        for df in dfs:
            if not df.empty:
                return df
        return pd.DataFrame()


def retry_with_backoff(func, max_retries: int = 3, base_delay: float = 1.0):
    """
    Decorator for retry logic with exponential backoff
    
    Args:
        func: Function to retry
        max_retries: Maximum number of retries
        base_delay: Base delay in seconds
        
    Returns:
        Function result or raises last exception
    """
    def wrapper(*args, **kwargs):
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    delay = base_delay * (2 ** attempt)
                    logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {str(e)}")
                    time.sleep(delay)
                else:
                    logger.error(f"All {max_retries + 1} attempts failed")
        
        raise last_exception
    
    return wrapper


def normalize_table_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Normalize table data for processing
    
    Args:
        df: Input DataFrame
        
    Returns:
        Normalized DataFrame
    """
    if df.empty:
        return df
    
    try:
        # Clean column names
        df.columns = [clean_text(str(col)) for col in df.columns]
        
        # Clean cell values
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].astype(str).apply(clean_text)
        
        # Remove empty rows
        df = df.dropna(how='all')
        
        # Remove rows where all values are empty strings
        df = df[~(df == '').all(axis=1)]
        
        return df.reset_index(drop=True)
        
    except Exception as e:
        logger.error(f"Table normalization error: {str(e)}")
        return df


def create_summary_stats(requirements: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create summary statistics for requirements
    
    Args:
        requirements: List of requirement dictionaries
        
    Returns:
        Summary statistics
    """
    if not requirements:
        return {
            'total_count': 0,
            'by_page': {},
            'by_method': {},
            'average_confidence': 0.0
        }
    
    # Count by page
    by_page = {}
    for req in requirements:
        page = req.get('source_page', 'unknown')
        by_page[str(page)] = by_page.get(str(page), 0) + 1
    
    # Count by method
    by_method = {}
    for req in requirements:
        method = req.get('extraction_method', 'unknown')
        by_method[method] = by_method.get(method, 0) + 1
    
    # Calculate average confidence
    confidences = [req.get('confidence', 0.5) for req in requirements]
    avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
    
    return {
        'total_count': len(requirements),
        'by_page': by_page,
        'by_method': by_method,
        'average_confidence': round(avg_confidence, 3)
    }