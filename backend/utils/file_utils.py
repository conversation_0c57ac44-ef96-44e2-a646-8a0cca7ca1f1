import pandas as pd
import os
import logging
from pathlib import Path

# Configure logging for this module
logger = logging.getLogger(__name__)

def create_excel(query, file_path):
    """Create Excel file from query data with error handling and logging"""
    try:
        logger.info(f"Creating Excel file: {file_path}")
        logger.debug(f"Query length: {len(query)}")
        
        # Ensure directory exists
        file_path_obj = Path(file_path)
        file_path_obj.parent.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Directory ensured: {file_path_obj.parent}")
        
        # Create DataFrame
        logger.debug("Creating DataFrame from query data")
        df = pd.DataFrame({"Query": [query]})
        
        # Write to Excel
        logger.debug(f"Writing Excel file to: {file_path}")
        df.to_excel(file_path, index=False)
        
        # Verify file was created
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            logger.info(f"Excel file created successfully: {file_path} ({file_size} bytes)")
        else:
            logger.error(f"Excel file was not created: {file_path}")
            raise FileNotFoundError(f"Failed to create Excel file: {file_path}")
            
    except Exception as e:
        logger.error(f"Failed to create Excel file {file_path}: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        raise