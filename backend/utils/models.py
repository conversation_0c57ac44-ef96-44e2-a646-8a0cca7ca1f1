"""
Data models and structures for the requirements extraction pipeline.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any


class ExtractionMethod(Enum):
    """Enumeration of extraction methods"""
    TABLE_EXTRACTION = "table_extraction"
    LLM_EXTRACTION = "llm_extraction"


@dataclass
class ExtractionResult:
    """Data class to hold extraction results"""
    method_used: ExtractionMethod
    success: bool
    data: Dict[str, Any]
    confidence_score: float
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'method_used': self.method_used.value,
            'success': self.success,
            'data': self.data,
            'confidence_score': self.confidence_score,
            'error_message': self.error_message
        }


@dataclass
class TableInfo:
    """Information about detected tables"""
    method: str
    page: int
    table_data: Any  # pandas DataFrame or similar
    confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'method': self.method,
            'page': self.page,
            'confidence': self.confidence,
            'has_data': self.table_data is not None
        }


@dataclass
class Requirement:
    """Single requirement item"""
    requirement: str
    witness: Optional[str] = None
    item: Optional[str] = None
    details: Optional[str] = None
    source_page: Optional[int] = None
    extraction_method: Optional[str] = None
    confidence: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'requirement': self.requirement,
            'witness': self.witness,
            'item': self.item,
            'details': self.details,
            'source_page': self.source_page,
            'extraction_method': self.extraction_method,
            'confidence': self.confidence
        }


@dataclass
class RequirementsSummary:
    """Summary of extracted requirements"""
    total_count: int
    by_page: Dict[int, int]
    by_method: Dict[str, int]
    average_confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format"""
        return {
            'total_count': self.total_count,
            'by_page': self.by_page,
            'by_method': self.by_method,
            'average_confidence': self.average_confidence
        }