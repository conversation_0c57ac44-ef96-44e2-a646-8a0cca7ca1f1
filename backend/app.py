from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from src.json_db_service import (
    get_connection, get_all_records, get_record_count, update_record, delete_record,
    get_dashboard_analytics, log_activity, get_recent_activities, reset_database,
    insert_webmethods_data
)
from src.config import Config
import os
import json
import logging
import sys
from datetime import datetime
from dotenv import load_dotenv
from pathlib import Path


# Configure logging with log folder
log_directory = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_directory, exist_ok=True)
log_file_path = os.path.join(log_directory, 'app.log')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Kansai Port API", version="1.0.0")

# Log startup
logger.info("Starting Kansai Port API...")

# Enable CORS for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for Code Engine deployment
    allow_credentials=False,  # Set to False when using allow_origins=["*"]
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load environment variables
try:
    dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
    logger.info(f"Loading .env from: {dotenv_path}")
    load_dotenv(dotenv_path)
    logger.info(".env file loaded successfully")
except Exception as e:
    logger.error(f"Failed to load .env file: {e}")
    sys.exit(1)

# Get upload folder and ensure it exists
UPLOAD_FOLDER = os.getenv("UPLOAD_FOLDER", "/tmp")
logger.info(f"Upload folder configured as: {UPLOAD_FOLDER}")

# Create required directories
def create_required_directories():
    """Create all required directories at startup"""
    directories = [
        UPLOAD_FOLDER,
        os.path.join(os.path.dirname(__file__), 'notebooks'),
        os.path.join(os.path.dirname(__file__), 'logs')
    ]
    
    for directory in directories:
        try:
            if not os.path.isabs(directory):
                directory = os.path.join(os.path.dirname(__file__), directory)
            
            Path(directory).mkdir(parents=True, exist_ok=True)
            logger.info(f"Directory ensured: {directory}")
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")
            raise

# Create directories at startup
try:
    create_required_directories()
    logger.info("All required directories created/verified")
except Exception as e:
    logger.error(f"Failed to create required directories: {e}")
    sys.exit(1)

# Initialize configuration
try:
    logger.info("Loading application configuration...")
    config = Config.from_env()
    logger.info("Configuration loaded from environment")
except Exception as e:
    logger.error(f"Failed to load configuration: {e}")
    sys.exit(1)

class AuthInput(BaseModel):
    password: str

class UpdateRecordInput(BaseModel):
    issue: str = None
    description: str = None
    box_link: str = None
    password: str

class DeleteRecordInput(BaseModel):
    password: str

class WebMethodsWebhookInput(BaseModel):
    metadata: str
    date: str
    issue: str
    description: str
    box_shared_link: str

class DashboardQueryParams(BaseModel):
    page: int = 1
    limit: int = 25
    search: str = ""
    source_filter: str = ""
    has_link: bool = None
    sort_by: str = "id"
    sort_order: str = "desc"

@app.get("/")
def read_root():
    """Root endpoint with API information"""
    try:
        logger.info("Root endpoint accessed")
        response = {
            "message": "Kansai Port API", 
            "version": "1.0.0",
            "active_endpoints": [
                "/health", "/api/config",
                "/api/dashboard", "/api/dashboard/record/{id}",
                "/api/dashboard/analytics", "/api/dashboard/activities", "/api/dashboard/health",
                "/api/dashboard/backup", "/api/dashboard/export", "/api/dashboard/reset",
                "/api/webhook/webmethods"
            ],
            "database_status": "operational"
        }
        logger.debug(f"Root response: {response}")
        return response
    except Exception as e:
        logger.error(f"Error in root endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/config")
def get_frontend_config():
    """Frontend configuration endpoint"""
    try:
        logger.info("Frontend config endpoint accessed")
        
        # Get URLs from environment variables with fallbacks
        # For dashboard API, use the current request's base URL if not explicitly set
        dashboard_api_url = os.getenv('DASHBOARD_API_URL')
        if not dashboard_api_url:
            # Auto-detect from the current request (will work in Code Engine)
            dashboard_api_url = "self"  # Special value meaning "use current domain"
        
        form_submit_url = os.getenv('FORM_SUBMIT_URL', 'https://env769838.int-aws-us.webmethods.io/runflow/run/1UWXeQANOg')
        
        config = {
            "dashboardApiUrl": dashboard_api_url,
            "formSubmitUrl": form_submit_url
        }
        
        logger.debug(f"Frontend config: {config}")
        return config
    except Exception as e:
        logger.error(f"Error in config endpoint: {e}")
        raise HTTPException(status_code=500, detail="Failed to get configuration")

@app.get("/health")
def health_check():
    """Health check endpoint with system status"""
    try:
        logger.info("Health check accessed")
        
        # Check database connection
        db_status = "unknown"
        try:
            conn = get_connection()
            conn.close()
            db_status = "connected"
            logger.debug("Database connection test successful")
        except Exception as db_e:
            db_status = f"error: {str(db_e)}"
            logger.warning(f"Database connection test failed: {db_e}")
        
        # Check upload folder
        upload_folder_status = "exists" if os.path.exists(UPLOAD_FOLDER) else "missing"
        
        response = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database_status": db_status,
            "upload_folder_status": upload_folder_status,
            "upload_folder_path": UPLOAD_FOLDER
        }
        
        logger.info(f"Health check response: {response}")
        return response
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

# Removed unused endpoints: /upload, /api/issues, /api/extract
# These endpoints were not being called by the current frontend

@app.get("/api/dashboard")
def get_dashboard_data():
    """Get dashboard data with all records and analytics"""
    logger.info("Dashboard data endpoint accessed")
    
    conn = None
    try:
        conn = get_connection()
        
        # Get all records
        records = get_all_records(conn, limit=config.dashboard.max_displayed_items)
        
        # Get total count
        total_count = get_record_count(conn)
        
        # Get analytics
        analytics = get_dashboard_analytics(conn)
        
        response = {
            "status": "success",
            "data": records,
            "total_count": total_count,
            "analytics": analytics,
            "config": {
                "items_per_page": config.dashboard.items_per_page,
                "auto_refresh_interval": config.dashboard.auto_refresh_interval,
                "theme": config.dashboard.theme,
                "enable_animations": config.dashboard.enable_animations
            }
        }
        
        logger.info(f"Dashboard data retrieved successfully: {len(records)} records")
        return response
        
    except Exception as e:
        logger.error(f"Error in dashboard data endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve dashboard data: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

# Removed unused endpoint: /api/dashboard/auth
# Authentication is now handled inline with individual operations

@app.put("/api/dashboard/record/{record_id}")
def update_dashboard_record(record_id: int, record_data: UpdateRecordInput):
    """Update a record in the dashboard"""
    logger.info(f"Dashboard record update endpoint accessed for ID: {record_id}")
    
    conn = None
    try:
        # Authenticate first
        if record_data.password not in config.dashboard.passwords:
            logger.warning("Unauthorized attempt to update record")
            raise HTTPException(status_code=401, detail="Invalid password")
        
        conn = get_connection()
        
        # Update the record
        success = update_record(conn, record_id, record_data.issue, record_data.description, record_data.box_link)
        
        if not success:
            logger.warning(f"Record not found for update: {record_id}")
            raise HTTPException(status_code=404, detail="Record not found")
        
        logger.info(f"Record updated successfully: {record_id}")
        return {
            "status": "success",
            "message": "Record updated successfully",
            "record_id": record_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating dashboard record: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update record: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

# Removed unused endpoint: /api/extract-table
# This endpoint was not being called by the current frontend

@app.delete("/api/dashboard/record/{record_id}")
def delete_dashboard_record(record_id: int, delete_data: DeleteRecordInput):
    """Delete a record from the dashboard"""
    logger.info(f"Dashboard record deletion endpoint accessed for ID: {record_id}")
    
    conn = None
    try:
        # Authenticate first
        if delete_data.password not in config.dashboard.passwords:
            logger.warning("Unauthorized attempt to delete record")
            raise HTTPException(status_code=401, detail="Invalid password")
        
        conn = get_connection()
        
        # Delete the record
        success = delete_record(conn, record_id)
        
        if not success:
            logger.warning(f"Record not found for deletion: {record_id}")
            raise HTTPException(status_code=404, detail="Record not found")
        
        logger.info(f"Record deleted successfully: {record_id}")
        return {
            "status": "success",
            "message": "Record deleted successfully",
            "record_id": record_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting dashboard record: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete record: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.post("/api/webhook/webmethods")
def handle_webmethods_webhook(webhook_data: WebMethodsWebhookInput):
    """Handle incoming webhook data from webmethods with separate fields"""
    logger.info(f"WebMethods webhook endpoint accessed with metadata: {webhook_data.metadata}")
    
    conn = None
    
    try:
        # Get database connection
        logger.info("Connecting to database for WebMethods webhook...")
        conn = get_connection()
        logger.info("Database connection established for WebMethods webhook")
        
        # Insert to DB with separate fields - NO concatenation needed!
        logger.info("Inserting WebMethods webhook data with separate fields...")
        row_id = insert_webmethods_data(
            conn, 
            webhook_data.date,
            webhook_data.issue,
            webhook_data.description,
            webhook_data.metadata,
            webhook_data.box_shared_link
        )
        logger.info(f"WebMethods webhook data inserted with ID: {row_id}")

        response = {
            "status": "success", 
            "message": "WebMethods webhook data processed successfully",
            "id": row_id,
            "box_shared_link": webhook_data.box_shared_link,
            "metadata": webhook_data.metadata,
            "date": webhook_data.date,
            "issue": webhook_data.issue,
            "description": webhook_data.description
        }
        logger.info(f"WebMethods webhook processing completed successfully: {response}")
        return response
        
    except Exception as e:
        logger.error(f"Error in WebMethods webhook endpoint: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        raise HTTPException(status_code=500, detail=f"Failed to process WebMethods webhook: {str(e)}")
        
    finally:
        if conn:
            try:
                conn.close()
                logger.debug("Database connection closed for WebMethods webhook")
            except Exception as close_e:
                logger.warning(f"Error closing database connection for WebMethods webhook: {close_e}")

@app.get("/api/dashboard/analytics")
def get_analytics():
    """Get comprehensive dashboard analytics"""
    logger.info("Dashboard analytics endpoint accessed")
    
    conn = None
    try:
        conn = get_connection()
        analytics = get_dashboard_analytics(conn)
        
        return {
            "status": "success",
            "analytics": analytics
        }
        
    except Exception as e:
        logger.error(f"Error getting analytics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.get("/api/dashboard/activities")
def get_activities(limit: int = 50):
    """Get recent dashboard activities"""
    logger.info("Dashboard activities endpoint accessed")
    
    conn = None
    try:
        conn = get_connection()
        activities = get_recent_activities(conn, limit)
        
        return {
            "status": "success",
            "activities": activities,
            "count": len(activities)
        }
        
    except Exception as e:
        logger.error(f"Error getting activities: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get activities: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.get("/api/dashboard/health")
def get_dashboard_health():
    """Get dashboard system health status"""
    logger.info("Dashboard health endpoint accessed")
    
    conn = None
    try:
        conn = get_connection()
        
        # Check database connectivity
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM query_links")
        total_records = cur.fetchone()[0]
        cur.close()
        
        # Check for orphaned records (missing box links older than 24h)
        cur = conn.cursor()
        cur.execute("""
            SELECT COUNT(*) FROM query_links 
            WHERE box_link IS NULL 
            AND id < (SELECT COALESCE(MAX(id), 0) - 100 FROM query_links)
        """)
        orphaned_records = cur.fetchone()[0]
        cur.close()
        
        # System health score
        health_score = 100
        issues = []
        
        if orphaned_records > 0:
            health_score -= min(20, orphaned_records * 2)
            issues.append(f"{orphaned_records} records missing box links")
        
        if total_records == 0:
            health_score = 50
            issues.append("No data in system")
        
        health_status = "excellent" if health_score >= 90 else "good" if health_score >= 70 else "fair" if health_score >= 50 else "poor"
        
        return {
            "status": "success",
            "health": {
                "score": health_score,
                "status": health_status,
                "total_records": total_records,
                "orphaned_records": orphaned_records,
                "issues": issues,
                "last_check": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard health: {e}")
        return {
            "status": "error",
            "health": {
                "score": 0,
                "status": "critical",
                "issues": [f"Health check failed: {str(e)}"],
                "last_check": datetime.now().isoformat()
            }
        }
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.post("/api/dashboard/backup")
def create_backup(auth: AuthInput):
    """Create a data backup"""
    logger.info("Dashboard backup endpoint accessed")
    
    if auth.password not in config.dashboard.passwords:
        logger.warning("Unauthorized backup attempt")
        raise HTTPException(status_code=401, detail="Invalid password")
    
    conn = None
    try:
        conn = get_connection()
        
        # Get all data
        cur = conn.cursor()
        cur.execute("SELECT id, queries, box_link FROM query_links ORDER BY id")
        records = cur.fetchall()
        cur.close()
        
        # Create backup data
        backup_data = {
            "timestamp": datetime.now().isoformat(),
            "version": "1.0",
            "record_count": len(records),
            "data": [
                {
                    "id": record[0],
                    "queries": record[1],
                    "box_link": record[2]
                }
                for record in records
            ]
        }
        
        # Create backup file
        backup_filename = f"dashboard_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        backup_path = os.path.join(UPLOAD_FOLDER, backup_filename)
        
        with open(backup_path, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, ensure_ascii=False, indent=2)
        
        # Log activity
        log_activity(conn, "backup_created", details=f"Backup file: {backup_filename}")
        
        logger.info(f"Backup created successfully: {backup_filename}")
        
        return {
            "status": "success",
            "message": "Backup created successfully",
            "filename": backup_filename,
            "record_count": len(records),
            "file_size": os.path.getsize(backup_path)
        }
        
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create backup: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.get("/api/dashboard/export")
def export_dashboard_data(format: str = "csv", auth_password: str = None):
    """Export dashboard data in various formats"""
    logger.info(f"Dashboard export endpoint accessed for format: {format}")
    
    if auth_password not in config.dashboard.passwords:
        logger.warning("Unauthorized export attempt")
        raise HTTPException(status_code=401, detail="Invalid password")
    
    conn = None
    try:
        conn = get_connection()
        
        # Get all data
        cur = conn.cursor()
        cur.execute("SELECT id, queries, box_link FROM query_links ORDER BY id")
        records = cur.fetchall()
        cur.close()
        
        if format.lower() == "csv":
            # Create CSV content
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(['ID', 'Queries', 'Box Link'])
            
            for record in records:
                writer.writerow([record[0], record[1], record[2] or ''])
            
            csv_content = output.getvalue()
            output.close()
            
            # Log activity
            log_activity(conn, "data_exported", details=f"Format: CSV, Records: {len(records)}")
            
            return {
                "status": "success",
                "format": "csv",
                "content": csv_content,
                "record_count": len(records),
                "filename": f"dashboard_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            }
            
        elif format.lower() == "json":
            # Create JSON content
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "record_count": len(records),
                "records": [
                    {
                        "id": record[0],
                        "queries": record[1],
                        "box_link": record[2]
                    }
                    for record in records
                ]
            }
            
            # Log activity
            log_activity(conn, "data_exported", details=f"Format: JSON, Records: {len(records)}")
            
            return {
                "status": "success",
                "format": "json",
                "content": json.dumps(export_data, ensure_ascii=False, indent=2),
                "record_count": len(records),
                "filename": f"dashboard_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            }
        
        else:
            raise HTTPException(status_code=400, detail="Unsupported export format. Use 'csv' or 'json'")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting data: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to export data: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection: {close_e}")

@app.post("/api/dashboard/reset")
def reset_dashboard_database(auth: AuthInput):
    """Reset database to initial state - WARNING: Deletes all data"""
    logger.warning("Database reset endpoint accessed")
    
    if auth.password not in config.dashboard.passwords:
        logger.warning("Unauthorized database reset attempt")
        raise HTTPException(status_code=401, detail="Invalid password")
    
    conn = None
    try:
        logger.warning("Starting database reset operation")
        conn = get_connection()
        
        # Perform the reset
        reset_info = reset_database(conn)
        
        logger.warning(f"Database reset completed successfully: {reset_info}")
        
        return {
            "status": "success",
            "message": "Database reset completed successfully",
            "reset_info": reset_info,
            "warning": "All data has been permanently deleted"
        }
        
    except Exception as e:
        logger.error(f"Error during database reset: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset database: {str(e)}")
    finally:
        if conn:
            try:
                conn.close()
            except Exception as close_e:
                logger.warning(f"Error closing database connection after reset: {close_e}")

if __name__ == "__main__":
    import uvicorn
    try:
        logger.info("Starting Uvicorn server...")
        logger.info("Server configuration: host=0.0.0.0, port=8080")
        uvicorn.run(app, host="0.0.0.0", port=8080, log_level="info")
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        sys.exit(1)