# Core dependencies
pandas>=1.5.0
requests>=2.28.1
python-dateutil>=2.8.2
python-dotenv>=1.0.0

# PDF processing and table extraction
camelot-py[cv]>=0.10.1
tabula-py>=2.5.1
pdfplumber>=0.7.6
PyPDF2>=3.0.1
opencv-python>=4.6.0
ghostscript>=0.7

# Excel file handling
openpyxl>=3.0.10

# Database (using JSON file-based storage)
# psycopg2-binary>=2.9.0  # Removed - using JSON database

# Cloud storage (Box)
boxsdk>=3.9.0

# AI/ML services
ibm-watsonx-ai>=1.0.0

# Web framework (FastAPI)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
pydantic>=2.0.0

# Web interface (Gradio)
gradio>=4.0.0

# Additional utilities
pathlib2>=2.3.7; python_version<"3.4"
PyYAML>=6.0