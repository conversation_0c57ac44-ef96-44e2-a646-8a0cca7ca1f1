# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/*.log

# Local development files
.pytest_cache/
.coverage
htmlcov/

# Docker
Dockerfile
.dockerignore

# Git
.git/
.gitignore

# Temporary files
tmp/
temp/

# Large data directories (adjust as needed)
ExcelData/
PdfData/
UploadedData/

# Jupyter notebooks
*.ipynb_checkpoints