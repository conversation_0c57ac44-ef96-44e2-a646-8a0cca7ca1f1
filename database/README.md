# PostgreSQL Database Container

This directory contains the PostgreSQL database container setup for the Kansai-port application.

## Files

- `Dockerfile` - PostgreSQL container configuration
- `init-db.sql` - Database initialization script with tables and sample data
- `README.md` - This file

## Database Schema

The database includes:

1. **query_links** table - Main data storage
   - `id` (SERIAL PRIMARY KEY)
   - `date` (VARCHAR)
   - `issue` (TEXT)
   - `description` (TEXT)
   - `metadata` (TEXT)
   - `queries` (TEXT)
   - `box_link` (TEXT)
   - `source` (VARCHAR)
   - `created_at` (TIMESTAMP)
   - `updated_at` (TIMESTAMP)

2. **activities** table - Activity logging
   - `id` (SERIAL PRIMARY KEY)
   - `activity_type` (VARCHAR)
   - `details` (TEXT)
   - `created_at` (TIMESTAMP)

## Environment Variables

- `POSTGRES_DB=querydb`
- `POSTGRES_USER=postgres`
- `POSTGRES_PASSWORD=Change_Me@12345`

## Deployment

1. Build the container:
   ```bash
   docker build -t kansai-port-db .
   ```

2. Run locally:
   ```bash
   docker run -p 5432:5432 kansai-port-db
   ```

3. Deploy to Code Engine and get the endpoint URL

4. Update backend `.env` file with the database endpoint:
   ```
   DB_HOST=<your-database-container-endpoint>
   DB_PORT=5432
   DB_NAME=querydb
   DB_USER=postgres
   DB_PASSWORD=Change_Me@12345
   ```

## Port

The database container exposes port 5432 (PostgreSQL default).