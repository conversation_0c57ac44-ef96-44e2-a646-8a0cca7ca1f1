-- Initialize database tables for Kansai-port application

-- Create query_links table with all required fields
CREATE TABLE IF NOT EXISTS query_links (
    id SERIAL PRIMARY KEY,
    date VARCHAR(255),
    issue TEXT,
    description TEXT,
    metadata TEXT,
    queries TEXT,
    box_link TEXT,
    source VARCHAR(50) DEFAULT 'unknown',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create activities table for logging
CREATE TABLE IF NOT EXISTS activities (
    id SERIAL PRIMARY KEY,
    activity_type VARCHAR(100) NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_query_links_source ON query_links(source);
CREATE INDEX IF NOT EXISTS idx_query_links_created_at ON query_links(created_at);
CREATE INDEX IF NOT EXISTS idx_activities_type ON activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON activities(created_at);

-- Insert sample data for testing (optional)
-- INSERT INTO query_links (issue, description, source) VALUES 
-- ('Sample Issue', 'Sample Description', 'initialization');

-- Log initialization
INSERT INTO activities (activity_type, details) VALUES 
('database_initialized', 'Database tables created successfully');