# Issue Report Form

A production-ready React frontend application with internationalization support for English and Japanese languages.

## Features

- 🌐 **Internationalization**: Support for English and Japanese languages
- 📱 **Responsive Design**: Mobile-friendly interface
- ✨ **Smooth Animations**: Production-level UI with smooth transitions
- 🔧 **Form Validation**: Required field validation
- 🚀 **Backend Integration**: API integration using environment variables

## Setup Instructions

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment variables**:
   - Copy `.env.example` to `.env`
   - Update `REACT_APP_BACKEND_URL` with your backend API URL
   ```bash
   cp .env.example .env
   ```

3. **Start the development server**:
   ```bash
   npm start
   ```

4. **Open your browser**:
   Navigate to `http://localhost:3000`

## Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_BACKEND_URL` | Backend API base URL | `http://localhost:3001` |

## Form Fields

The form includes the following fields:
- **Date**: Date picker for selecting the issue date
- **Issue**: Text input for the issue title
- **Description**: Textarea for detailed issue description

## Language Support

- **English** (default)
- **Japanese** (日本語)

Switch languages using the dropdown in the top-right corner.

## API Integration

The form submits data to `${REACT_APP_BACKEND_URL}/api/issues` with the following structure:

```json
{
  "date": "2023-12-01",
  "issue": "Sample issue title",
  "description": "Detailed description of the issue"
}
```

## Available Scripts

### `npm start`
Runs the app in development mode at [http://localhost:3000](http://localhost:3000)

### `npm run build`
Builds the app for production to the `build` folder

### `npm test`
Launches the test runner in interactive watch mode

## Technologies Used

- React 18 with TypeScript
- react-i18next (internationalization)
- Axios (HTTP client)
- CSS3 (animations and styling)