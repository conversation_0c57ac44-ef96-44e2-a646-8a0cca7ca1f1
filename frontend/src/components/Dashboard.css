/* Dashboard Container */
.dashboard-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(226, 232, 240, 0.8);
  animation: fadeInScale 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    #3b82f6 0%,
    #1d4ed8 50%,
    #1e40af 100%
  );
  border-radius: 16px 16px 0 0;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-top: 8px;
}

.dashboard-title {
  font-size: clamp(24px, 3vw, 32px);
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.5px;
}

.dashboard-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.record-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  padding: 8px 16px;
  background: #f1f5f9;
  border-radius: 20px;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.refresh-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-button.refreshing .refresh-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Dashboard Table */
.dashboard-table {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 150px 140px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
}

.header-cell {
  padding: 16px 12px;
  font-weight: 700;
  color: #374151;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-right: 1px solid #e2e8f0;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  max-height: 60vh;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 150px 140px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
  transform: scale(1.005);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 16px 12px;
  border-right: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  word-break: break-word;
}

.table-cell:last-child {
  border-right: none;
}

.cell-id {
  font-weight: 600;
  color: #3b82f6;
  justify-content: center;
  font-size: 14px;
}

.queries-content {
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.box-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  background: #eff6ff;
  border: 1px solid #dbeafe;
  transition: all 0.2s ease;
  font-size: 12px;
}

.box-link:hover {
  background: #dbeafe;
  transform: translateY(-1px);
}

.no-link {
  color: #9ca3af;
  font-size: 12px;
  font-style: italic;
}

.cell-actions {
  gap: 8px;
  justify-content: center;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.edit-button {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fed7aa;
}

.edit-button:hover {
  background: #fed7aa;
  transform: translateY(-1px);
}

.delete-button {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.delete-button:hover {
  background: #fecaca;
  transform: translateY(-1px);
}

/* Loading States */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 24px;
  max-width: 400px;
}

.retry-button {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.dashboard-empty {
  text-align: center;
  color: #64748b;
  padding: 80px 20px;
  font-size: 18px;
}

/* Dialog Styles */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.dialog {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.dialog-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
}

.dialog-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.dialog-close:hover {
  background: #f1f5f9;
  color: #374151;
}

.dialog-body {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
}

.edit-textarea,
.edit-input,
.auth-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.edit-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.edit-textarea:focus,
.edit-input:focus,
.auth-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.dialog-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.cancel-button {
  background: #f1f5f9;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-button:hover:not(:disabled) {
  background: #e2e8f0;
}

.save-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.save-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
}

.confirm-button {
  color: white;
}

.confirm-button.edit {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.confirm-button.edit:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.confirm-button.delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.confirm-button.delete:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.dialog-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.auth-dialog .dialog-body p {
  margin-bottom: 20px;
  color: #374151;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-container {
    padding: 20px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 70px 1fr 130px 120px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
    margin: 0 8px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .dashboard-controls {
    justify-content: space-between;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 60px 1fr 100px;
  }
  
  .header-cell:nth-child(3),
  .table-cell:nth-child(3) {
    display: none;
  }
  
  .header-cell:nth-child(4),
  .table-cell:nth-child(4) {
    grid-column: 3;
  }
  
  .cell-actions {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-button {
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .dialog {
    width: 95%;
    margin: 20px;
  }
  
  .dialog-footer {
    flex-direction: column-reverse;
  }
  
  .dialog-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .dashboard-title {
    font-size: 20px;
  }
  
  .table-header,
  .table-row {
    grid-template-columns: 50px 1fr 80px;
  }
  
  .table-cell {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .queries-content {
    font-size: 12px;
  }
}