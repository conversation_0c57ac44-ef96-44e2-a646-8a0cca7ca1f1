/* Form Container - Professional Design */
.form-container {
  width: 100%;
  max-width: none;
  margin: 0;
  padding: 48px;
  background: #ffffff;
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px;
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.12);
  animation: fadeInScale 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  min-height: 70vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Subtle professional accent */
.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    #3b82f6 0%,
    #1d4ed8 50%,
    #1e40af 100%
  );
  border-radius: 16px 16px 0 0;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(30px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.form-title {
  text-align: center;
  color: #1e293b;
  font-size: clamp(28px, 4vw, 42px);
  font-weight: 500;
  margin-bottom: 48px;
  letter-spacing: -0.3px;
  position: relative;
  z-index: 1;
  line-height: 1.2;
}

.issue-form {
  display: grid;
  gap: 32px;
  position: relative;
  z-index: 1;
  flex: 1;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.form-group.focused {
  transform: scale(1.02);
}

.form-group.focused .form-label {
  color: #3b82f6;
  transform: translateY(-2px);
}

.form-label {
  font-weight: 500;
  color: #374151;
  font-size: clamp(14px, 2vw, 16px);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.form-input,
.form-textarea {
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: clamp(16px, 2.5vw, 18px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: #ffffff;
  color: #2d3748;
  box-shadow: 
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.form-textarea {
  resize: vertical;
  min-height: clamp(120px, 15vh, 200px);
  font-family: inherit;
  line-height: 1.6;
}

.submit-button {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  padding: 18px 36px;
  border-radius: 12px;
  font-size: clamp(16px, 2.5vw, 18px);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-top: 24px;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 
    0 8px 24px rgba(59, 130, 246, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: #9ca3af;
}

.submit-button.submitting {
  animation: pulse 2s infinite;
}

.submit-button.submitting::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3), 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3), 0 0 0 16px rgba(59, 130, 246, 0);
  }
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.message {
  padding: 16px 20px;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
  animation: slideInBounce 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin-top: 24px;
  font-size: clamp(14px, 2vw, 16px);
}

@keyframes slideInBounce {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  50% {
    transform: translateY(5px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message.success {
  background: #d1fae5;
  color: #065f46;
  border: 2px solid #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

.message.error {
  background: #fee2e2;
  color: #991b1b;
  border: 2px solid #ef4444;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

/* Ultra-responsive breakpoints */
@media (min-width: 1921px) {
  .form-container {
    padding: 60px;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  .issue-form {
    gap: 40px;
  }
}

@media (max-width: 1440px) {
  .form-container {
    padding: 36px;
  }
}

@media (max-width: 1200px) {
  .form-container {
    padding: 32px;
    min-height: 65vh;
  }
  
  .issue-form {
    gap: 28px;
  }
}

@media (max-width: 992px) {
  .form-container {
    padding: 28px;
    min-height: 60vh;
    border-radius: 20px;
  }
  
  .form-title {
    margin-bottom: 36px;
  }
  
  .issue-form {
    gap: 24px;
  }
  
  .form-input,
  .form-textarea {
    padding: 18px 20px;
  }
  
  .submit-button {
    padding: 18px 32px;
  }
}

@media (max-width: 768px) {
  .form-container {
    padding: 24px;
    min-height: 55vh;
    border-radius: 16px;
  }
  
  .form-title {
    margin-bottom: 32px;
  }
  
  .issue-form {
    gap: 20px;
  }
  
  .form-input,
  .form-textarea {
    padding: 16px 18px;
  }
  
  .submit-button {
    padding: 16px 28px;
    margin-top: 20px;
  }
  
  .form-textarea {
    min-height: 100px;
  }
}

@media (max-width: 576px) {
  .form-container {
    padding: 20px;
    min-height: 50vh;
    border-radius: 12px;
  }
  
  .form-title {
    margin-bottom: 28px;
  }
  
  .issue-form {
    gap: 18px;
  }
  
  .form-input,
  .form-textarea {
    padding: 14px 16px;
  }
  
  .submit-button {
    padding: 14px 24px;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .form-container {
    padding: 16px;
    border-radius: 8px;
  }
  
  .form-title {
    margin-bottom: 24px;
  }
  
  .issue-form {
    gap: 16px;
  }
}