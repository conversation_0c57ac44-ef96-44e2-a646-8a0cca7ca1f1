import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useConfig } from '../hooks/useConfig';
import './AnalyticsDashboard.css';

interface Analytics {
  summary: {
    total_records: number;
    records_with_links: number;
    records_without_links: number;
    completion_rate: number;
    recent_activity: number;
  };
  source_breakdown: Record<string, number>;
  top_detailed_records: Array<{
    id: number;
    length: number;
  }>;
  trends: {
    completion_trend: string;
    activity_level: string;
  };
}

interface HealthData {
  score: number;
  status: string;
  total_records: number;
  orphaned_records: number;
  issues: string[];
  last_check: string;
}

const AnalyticsDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [health, setHealth] = useState<HealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { config, loading: configLoading } = useConfig();

  const fetchAnalytics = async () => {
    try {
      if (!config?.dashboardApiUrl) {
        console.warn('Dashboard API URL not available for analytics');
        return;
      }

      const [analyticsResponse, healthResponse] = await Promise.all([
        axios.get(`${config.dashboardApiUrl}/api/dashboard/analytics`),
        axios.get(`${config.dashboardApiUrl}/api/dashboard/health`)
      ]);

      setAnalytics(analyticsResponse.data.analytics);
      setHealth(healthResponse.data.health);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Failed to load analytics');
      console.error('Analytics fetch error:', err);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      if (!config?.dashboardApiUrl || configLoading) {
        return; // Wait for config to load
      }

      setLoading(true);
      await fetchAnalytics();
      setLoading(false);
    };

    loadData();
    
    // Auto-refresh every 5 minutes (only if config is available)
    if (config?.dashboardApiUrl && !configLoading) {
      const interval = setInterval(fetchAnalytics, 300000);
      return () => clearInterval(interval);
    }
  }, [config?.dashboardApiUrl, configLoading]);

  const getHealthColor = (score: number) => {
    if (score >= 90) return '#10b981'; // green
    if (score >= 70) return '#f59e0b'; // yellow
    if (score >= 50) return '#f97316'; // orange
    return '#ef4444'; // red
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return '📈';
      case 'decreasing': return '📉';
      case 'stable': return '📊';
      default: return '➖';
    }
  };

  const getActivityIcon = (level: string) => {
    switch (level) {
      case 'high': return '🔥';
      case 'moderate': return '🟡';
      case 'low': return '🔵';
      default: return '⚪';
    }
  };

  if (loading) {
    return (
      <div className="analytics-container">
        <div className="analytics-loading">
          <div className="loading-spinner"></div>
          <span>{t('loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="analytics-container">
        <div className="analytics-error">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error}</div>
          <button onClick={fetchAnalytics} className="retry-button">
            {t('retry')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="analytics-container">
      {/* Health Status Banner */}
      {health && (
        <div className={`health-banner health-${health.status}`}>
          <div className="health-content">
            <div className="health-score">
              <div 
                className="health-circle"
                style={{ background: `conic-gradient(${getHealthColor(health.score)} ${health.score * 3.6}deg, #e5e7eb 0deg)` }}
              >
                <span className="health-score-text">{health.score}</span>
              </div>
            </div>
            <div className="health-info">
              <h3>System Health: {health.status.toUpperCase()}</h3>
              <p>{health.total_records} total records • {health.orphaned_records} need attention</p>
              {health.issues.length > 0 && (
                <div className="health-issues">
                  {health.issues.map((issue, index) => (
                    <span key={index} className="issue-tag">{issue}</span>
                  ))}
                </div>
              )}
            </div>
            <div className="health-actions">
              <button onClick={fetchAnalytics} className="refresh-health-btn">
                🔄 Refresh
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Cards */}
      {analytics && (
        <>
          <div className="analytics-summary">
            <div className="summary-card">
              <div className="summary-value">{analytics.summary.total_records}</div>
              <div className="summary-label">Total Records</div>
              <div className="summary-trend">
                {getTrendIcon(analytics.trends.completion_trend)} 
                {analytics.trends.completion_trend}
              </div>
            </div>
            
            <div className="summary-card">
              <div className="summary-value">{analytics.summary.completion_rate}%</div>
              <div className="summary-label">Completion Rate</div>
              <div className="summary-progress">
                <div 
                  className="progress-bar" 
                  style={{ width: `${analytics.summary.completion_rate}%` }}
                ></div>
              </div>
            </div>
            
            <div className="summary-card">
              <div className="summary-value">{analytics.summary.recent_activity}</div>
              <div className="summary-label">Recent Activity</div>
              <div className="summary-trend">
                {getActivityIcon(analytics.trends.activity_level)} 
                {analytics.trends.activity_level}
              </div>
            </div>
            
            <div className="summary-card">
              <div className="summary-value">{analytics.summary.records_without_links}</div>
              <div className="summary-label">Pending Links</div>
              <div className="summary-indicator">
                {analytics.summary.records_without_links > 0 ? '⚠️' : '✅'}
              </div>
            </div>
          </div>

          {/* Data Source Breakdown */}
          <div className="analytics-section">
            <h3>📊 Data Sources</h3>
            <div className="source-breakdown">
              {Object.entries(analytics.source_breakdown).map(([source, count]) => {
                const percentage = (count / analytics.summary.total_records) * 100;
                return (
                  <div key={source} className="source-item">
                    <div className="source-header">
                      <span className="source-name">{source}</span>
                      <span className="source-count">{count} ({percentage.toFixed(1)}%)</span>
                    </div>
                    <div className="source-bar">
                      <div 
                        className={`source-progress source-${source.toLowerCase()}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Top Detailed Records */}
          <div className="analytics-section">
            <h3>📝 Most Detailed Records</h3>
            <div className="detailed-records">
              {analytics.top_detailed_records.map((record, index) => (
                <div key={record.id} className="detailed-record-item">
                  <div className="record-rank">#{index + 1}</div>
                  <div className="record-info">
                    <span className="record-id">Record #{record.id}</span>
                    <span className="record-length">{record.length.toLocaleString()} characters</span>
                  </div>
                  <div className="record-bar">
                    <div 
                      className="record-progress"
                      style={{ 
                        width: `${(record.length / Math.max(...analytics.top_detailed_records.map(r => r.length))) * 100}%` 
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Quick Stats Grid */}
          <div className="quick-stats">
            <div className="stat-item">
              <div className="stat-icon">📁</div>
              <div className="stat-details">
                <div className="stat-value">{analytics.summary.records_with_links}</div>
                <div className="stat-label">With Links</div>
              </div>
            </div>
            
            <div className="stat-item">
              <div className="stat-icon">📋</div>
              <div className="stat-details">
                <div className="stat-value">{analytics.summary.records_without_links}</div>
                <div className="stat-label">Without Links</div>
              </div>
            </div>
            
            <div className="stat-item">
              <div className="stat-icon">⚡</div>
              <div className="stat-details">
                <div className="stat-value">{analytics.summary.recent_activity}</div>
                <div className="stat-label">Recent Items</div>
              </div>
            </div>
          </div>

          {/* Last Updated */}
          <div className="analytics-footer">
            <span className="last-updated">
              🕒 Last updated: {new Date().toLocaleTimeString()}
            </span>
            <button onClick={fetchAnalytics} className="refresh-analytics-btn">
              🔄 Refresh Analytics
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default AnalyticsDashboard;