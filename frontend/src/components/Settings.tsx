import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import './Settings.css';

interface SettingsConfig {
  dashboardApiUrl: string;
  formSubmitUrl: string;
}

interface SettingsProps {
  onClose: () => void;
  onSuccess: (message: string) => void;
  onError: (message: string) => void;
  isModal?: boolean;
}

const Settings: React.FC<SettingsProps> = ({ onClose, onSuccess, onError, isModal = true }) => {
  const { t } = useTranslation();
  const [config, setConfig] = useState<SettingsConfig>({
    dashboardApiUrl: '',
    formSubmitUrl: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<{
    dashboard: 'unknown' | 'connected' | 'failed';
    database: 'unknown' | 'connected' | 'failed';
  }>({
    dashboard: 'unknown',
    database: 'unknown'
  });

  useEffect(() => {
    loadCurrentSettings();
  }, []);

  const loadCurrentSettings = async () => {
    try {
      setLoading(true);
      
      // Load from localStorage first
      const savedConfig = localStorage.getItem('kansai-settings');
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig(parsedConfig);
      } else {
        // Fallback to default values
        setConfig({
          dashboardApiUrl: window.location.origin,
          formSubmitUrl: 'https://env769838.int-aws-us.webmethods.io/runflow/run/1UWXeQANOg'
        });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      onError('Failed to load current settings');
    } finally {
      setLoading(false);
    }
  };

  const testConnections = async () => {
    setTesting(true);
    setConnectionStatus({ dashboard: 'unknown', database: 'unknown' });

    // Test dashboard API connection
    try {
      const response = await axios.get(`${config.dashboardApiUrl}/health`, {
        timeout: 10000
      });
      
      if (response.status === 200) {
        setConnectionStatus(prev => ({ ...prev, dashboard: 'connected' }));
        
        // Check database status from health response
        if (response.data.database_status === 'connected') {
          setConnectionStatus(prev => ({ ...prev, database: 'connected' }));
        } else {
          setConnectionStatus(prev => ({ ...prev, database: 'failed' }));
        }
      } else {
        setConnectionStatus(prev => ({ ...prev, dashboard: 'failed', database: 'failed' }));
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setConnectionStatus({ dashboard: 'failed', database: 'failed' });
    } finally {
      setTesting(false);
    }
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      
      // Validate URLs
      if (!config.dashboardApiUrl || !config.formSubmitUrl) {
        onError('Both URLs are required');
        return;
      }

      try {
        new URL(config.dashboardApiUrl);
        new URL(config.formSubmitUrl);
      } catch {
        onError('Please enter valid URLs');
        return;
      }

      // Save to localStorage
      localStorage.setItem('kansai-settings', JSON.stringify(config));
      
      // Test connection after saving
      await testConnections();
      
      onSuccess('Settings saved successfully! Page will reload to apply changes.');
      
      // Reload page after a short delay to apply new settings
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('Failed to save settings:', error);
      onError('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setConfig({
      dashboardApiUrl: window.location.origin,
      formSubmitUrl: 'https://env769838.int-aws-us.webmethods.io/runflow/run/1UWXeQANOg'
    });
    setConnectionStatus({ dashboard: 'unknown', database: 'unknown' });
  };

  const getStatusIcon = (status: 'unknown' | 'connected' | 'failed') => {
    switch (status) {
      case 'connected': return '✅';
      case 'failed': return '❌';
      default: return '❓';
    }
  };

  const getStatusText = (status: 'unknown' | 'connected' | 'failed') => {
    switch (status) {
      case 'connected': return 'Connected';
      case 'failed': return 'Failed';
      default: return 'Unknown';
    }
  };

  if (loading) {
    const loadingContent = (
      <div className="settings-dialog">
        <div className="settings-loading">
          <div className="loading-spinner"></div>
          <span>Loading settings...</span>
        </div>
      </div>
    );
    
    return isModal ? (
      <div className="settings-overlay">{loadingContent}</div>
    ) : loadingContent;
  }

  const settingsContent = (
    <div className="settings-dialog">
        <div className="settings-header">
          <h2>⚙️ Application Settings</h2>
          <button onClick={onClose} className="settings-close">×</button>
        </div>

        <div className="settings-body">
          <div className="settings-section">
            <h3>🌐 Backend Configuration</h3>
            <p className="section-description">
              Configure the backend API URLs for the application. These settings are stored locally in your browser.
            </p>

            <div className="form-group">
              <label htmlFor="dashboardApiUrl">Dashboard API URL</label>
              <input
                id="dashboardApiUrl"
                type="url"
                value={config.dashboardApiUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, dashboardApiUrl: e.target.value }))}
                placeholder="http://localhost:8000"
                className="settings-input"
              />
              <small className="input-help">
                The base URL for the backend API (e.g., http://localhost:8000)
              </small>
            </div>

            <div className="form-group">
              <label htmlFor="formSubmitUrl">Form Submit URL</label>
              <input
                id="formSubmitUrl"
                type="url"
                value={config.formSubmitUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, formSubmitUrl: e.target.value }))}
                placeholder="https://your-webmethods-url"
                className="settings-input"
              />
              <small className="input-help">
                The URL for webMethods form submission
              </small>
            </div>
          </div>

          <div className="settings-section">
            <h3>🔗 Connection Status</h3>
            <div className="connection-status">
              <div className="status-item">
                <span className="status-label">Dashboard API:</span>
                <span className={`status-value ${connectionStatus.dashboard}`}>
                  {getStatusIcon(connectionStatus.dashboard)} {getStatusText(connectionStatus.dashboard)}
                </span>
              </div>
              <div className="status-item">
                <span className="status-label">Database:</span>
                <span className={`status-value ${connectionStatus.database}`}>
                  {getStatusIcon(connectionStatus.database)} {getStatusText(connectionStatus.database)}
                </span>
              </div>
            </div>
            
            <button
              onClick={testConnections}
              disabled={testing}
              className="test-button"
            >
              {testing ? '🔄 Testing...' : '🧪 Test Connections'}
            </button>
          </div>
        </div>

        <div className="settings-footer">
          <div className="button-group">
            <button
              onClick={resetToDefaults}
              className="reset-button"
            >
              🔄 Reset to Defaults
            </button>
            <button
              onClick={onClose}
              className="cancel-button"
            >
              Cancel
            </button>
            <button
              onClick={saveSettings}
              disabled={saving}
              className="save-button"
            >
              {saving ? '💾 Saving...' : '💾 Save Settings'}
            </button>
          </div>
        </div>
      </div>
  );

  return isModal ? (
    <div className="settings-overlay">{settingsContent}</div>
  ) : settingsContent;
};

export default Settings;