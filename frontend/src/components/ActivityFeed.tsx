import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useConfig } from '../hooks/useConfig';
import './ActivityFeed.css';

interface Activity {
  timestamp: string;
  action: string;
  record_id?: number;
  user_ip?: string;
  details?: string;
  success: boolean;
}

interface ActivityFeedProps {
  isCompact?: boolean;
  limit?: number;
  refreshInterval?: number;
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({ 
  isCompact = false, 
  limit = 50, 
  refreshInterval = 30000 
}) => {
  const { t } = useTranslation();
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const { config, loading: configLoading } = useConfig();

  const fetchActivities = async () => {
    try {
      if (!config?.dashboardApiUrl) {
        console.warn('Dashboard API URL not available for activities');
        return;
      }

      const response = await axios.get(`${config.dashboardApiUrl}/api/dashboard/activities?limit=${limit}`);
      setActivities(response.data.activities);
      setLastUpdated(new Date());
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Failed to load activities');
      console.error('Activities fetch error:', err);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      if (!config?.dashboardApiUrl || configLoading) {
        return; // Wait for config to load
      }

      setLoading(true);
      await fetchActivities();
      setLoading(false);
    };

    loadData();
    
    // Auto-refresh (only if config is available)
    if (config?.dashboardApiUrl && !configLoading) {
      const interval = setInterval(fetchActivities, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [config?.dashboardApiUrl, configLoading, limit, refreshInterval]);

  const getActionIcon = (action: string): string => {
    const actionMap: Record<string, string> = {
      'record_created': '➕',
      'record_updated': '✏️',
      'record_deleted': '🗑️',
      'bulk_delete': '🗂️',
      'data_exported': '📤',
      'backup_created': '💾',
      'dashboard_accessed': '👁️',
      'login_attempt': '🔐',
      'webhook_received': '🔗',
      'file_uploaded': '📁',
      'error_occurred': '⚠️',
      'system_health_check': '🏥'
    };
    return actionMap[action] || '📋';
  };

  const getActionColor = (action: string, success: boolean): string => {
    if (!success) return '#ef4444'; // red for errors
    
    const colorMap: Record<string, string> = {
      'record_created': '#10b981',
      'record_updated': '#3b82f6',
      'record_deleted': '#f59e0b',
      'bulk_delete': '#f97316',
      'data_exported': '#8b5cf6',
      'backup_created': '#06b6d4',
      'dashboard_accessed': '#6b7280',
      'webhook_received': '#10b981',
      'file_uploaded': '#059669'
    };
    return colorMap[action] || '#6b7280';
  };

  const formatAction = (action: string): string => {
    const actionMap: Record<string, string> = {
      'record_created': 'Record Created',
      'record_updated': 'Record Updated',
      'record_deleted': 'Record Deleted',
      'bulk_delete': 'Bulk Delete',
      'data_exported': 'Data Exported',
      'backup_created': 'Backup Created',
      'dashboard_accessed': 'Dashboard Accessed',
      'webhook_received': 'Webhook Received',
      'file_uploaded': 'File Uploaded',
      'error_occurred': 'Error Occurred',
      'system_health_check': 'Health Check',
      'webmethods_data_inserted': 'WebMethods Data Received',
      'database_initialized': 'Database Initialized',
      'database_reset': 'Database Reset'
    };
    if (!action) {
      return 'Unknown Action';
    }
    return actionMap[action] || action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTimeAgo = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const getActivityDescription = (activity: Activity): string => {
    let description = formatAction(activity.action);
    
    if (activity.record_id) {
      description += ` (Record #${activity.record_id})`;
    }
    
    if (activity.details) {
      description += ` - ${activity.details}`;
    }

    return description;
  };

  if (loading) {
    return (
      <div className={`activity-feed ${isCompact ? 'compact' : ''}`}>
        <div className="activity-header">
          <h3>📈 Recent Activity</h3>
        </div>
        <div className="activity-loading">
          <div className="loading-spinner-small"></div>
          <span>Loading activities...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`activity-feed ${isCompact ? 'compact' : ''}`}>
        <div className="activity-header">
          <h3>📈 Recent Activity</h3>
        </div>
        <div className="activity-error">
          <div className="error-message">{error}</div>
          <button onClick={fetchActivities} className="retry-btn-small">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`activity-feed ${isCompact ? 'compact' : ''}`}>
      <div className="activity-header">
        <h3>📈 Recent Activity</h3>
        <div className="activity-controls">
          {lastUpdated && (
            <span className="last-updated-small">
              Updated {formatTimeAgo(lastUpdated.toISOString())}
            </span>
          )}
          <button onClick={fetchActivities} className="refresh-btn-small">
            🔄
          </button>
        </div>
      </div>

      <div className="activity-list">
        {activities.length === 0 ? (
          <div className="no-activities">
            <div className="no-activities-icon">📋</div>
            <p>No recent activities</p>
          </div>
        ) : (
          activities.map((activity, index) => (
            <div 
              key={`${activity.timestamp}-${index}`} 
              className={`activity-item ${!activity.success ? 'activity-error' : ''}`}
            >
              <div className="activity-timeline">
                <div 
                  className="activity-dot"
                  style={{ backgroundColor: getActionColor(activity.action, activity.success) }}
                >
                  <span className="activity-icon">
                    {getActionIcon(activity.action)}
                  </span>
                </div>
                {index < activities.length - 1 && <div className="activity-line"></div>}
              </div>
              
              <div className="activity-content">
                <div className="activity-main">
                  <div className="activity-description">
                    {getActivityDescription(activity)}
                  </div>
                  <div className="activity-time">
                    {formatTimeAgo(activity.timestamp)}
                  </div>
                </div>
                
                {!isCompact && (
                  <div className="activity-metadata">
                    {activity.user_ip && (
                      <span className="activity-ip">
                        🌐 {activity.user_ip}
                      </span>
                    )}
                    {!activity.success && (
                      <span className="activity-status error">
                        ❌ Failed
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {!isCompact && activities.length > 0 && (
        <div className="activity-footer">
          <div className="activity-summary">
            {activities.length} recent activities
          </div>
          <button onClick={fetchActivities} className="load-more-btn">
            🔄 Refresh
          </button>
        </div>
      )}
    </div>
  );
};

export default ActivityFeed;