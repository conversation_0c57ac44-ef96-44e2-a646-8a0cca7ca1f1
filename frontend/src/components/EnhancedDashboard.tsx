import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useConfig } from '../hooks/useConfig';
import AnalyticsDashboard from './AnalyticsDashboard';
import ActivityFeed from './ActivityFeed';
import BackupManager from './BackupManager';
import './EnhancedDashboard.css';

interface DashboardRecord {
  id: number;
  date: string | null;
  issue: string | null;
  description: string | null;
  metadata: string | null;
  queries: string | null;
  box_link: string | null;
  source: string | null;
  created_at: string;
  updated_at: string;
}

interface DashboardData {
  data: DashboardRecord[];
  total_count: number;
  config: {
    items_per_page: number;
    auto_refresh_interval: number;
    theme: string;
    enable_animations: boolean;
  };
}

interface EditDialogState {
  isOpen: boolean;
  record: DashboardRecord | null;
  issue: string;
  description: string;
  box_link: string;
}

interface AuthDialogState {
  isOpen: boolean;
  action: 'edit' | 'delete' | 'bulkDelete';
  record: DashboardRecord | null;
  selectedRecords?: DashboardRecord[];
  password: string;
  isConfirming: boolean;
}

interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'info';
  message: string;
}

type SortColumn = 'id' | 'date' | 'issue' | 'description' | 'box_link';
type SortDirection = 'asc' | 'desc';
type DashboardTab = 'data' | 'analytics' | 'activity';

const EnhancedDashboard: React.FC = () => {
  const { t } = useTranslation();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Search and Filter
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [dateFilter, setDateFilter] = useState('');

  // Selection and Bulk Operations
  const [selectedRecords, setSelectedRecords] = useState<Set<number>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Sorting
  const [sortColumn, setSortColumn] = useState<SortColumn>('id');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);

  // Dialogs
  const [editDialog, setEditDialog] = useState<EditDialogState>({
    isOpen: false,
    record: null,
    issue: '',
    description: '',
    box_link: ''
  });

  const [authDialog, setAuthDialog] = useState<AuthDialogState>({
    isOpen: false,
    action: 'edit',
    record: null,
    password: '',
    isConfirming: false
  });

  // Toast notifications
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  // Tab management
  const [activeTab, setActiveTab] = useState<DashboardTab>('data');

  const { config, loading: configLoading, error: configError } = useConfig();

  // Add toast notification
  const addToast = useCallback((type: ToastMessage['type'], message: string) => {
    const id = Math.random().toString(36).substring(7);
    setToasts(prev => [...prev, { id, type, message }]);
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, 5000);
  }, []);

  // Fetch dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      if (!config?.dashboardApiUrl) {
        console.warn('Dashboard API URL not available yet');
        return;
      }

      const response = await axios.get(`${config.dashboardApiUrl}/api/dashboard`);
      setData(response.data);
      setLastUpdated(new Date());
      setError(null);
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || t('loadError');
      setError(errorMsg);
      console.error('Dashboard data fetch error:', err);
    }
  }, [config?.dashboardApiUrl, t]);

  // Refresh data
  const refreshData = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
    addToast('success', t('dashboardRefreshed') || 'Dashboard refreshed');
  };

  // Filter and sort data
  const filteredAndSortedData = useMemo(() => {
    if (!data?.data) return [];

    let filtered = data.data.filter(record => {
      const matchesSearch = !searchTerm || 
        record.id.toString().includes(searchTerm) ||
        (record.date && record.date.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (record.issue && record.issue.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (record.description && record.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (record.box_link && record.box_link.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesSearch;
    });

    // Sort data
    filtered.sort((a, b) => {
      let aVal: any, bVal: any;
      
      switch (sortColumn) {
        case 'id':
          aVal = a.id;
          bVal = b.id;
          break;
        case 'date':
          aVal = a.date || '';
          bVal = b.date || '';
          break;
        case 'issue':
          aVal = (a.issue || '').toLowerCase();
          bVal = (b.issue || '').toLowerCase();
          break;
        case 'description':
          aVal = (a.description || '').toLowerCase();
          bVal = (b.description || '').toLowerCase();
          break;
        case 'box_link':
          aVal = a.box_link || '';
          bVal = b.box_link || '';
          break;
        default:
          return 0;
      }

      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [data?.data, searchTerm, sortColumn, sortDirection]);

  // Paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredAndSortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredAndSortedData, currentPage, itemsPerPage]);

  // Statistics
  const statistics = useMemo(() => {
    if (!data?.data) return { total: 0, withLinks: 0, withoutLinks: 0 };
    
    const total = data.data.length;
    const withLinks = data.data.filter(record => record.box_link).length;
    const withoutLinks = total - withLinks;

    return { total, withLinks, withoutLinks };
  }, [data?.data]);

  // Initial load - only when config is available
  useEffect(() => {
    const loadData = async () => {
      if (!config?.dashboardApiUrl || configLoading) {
        return; // Wait for config to load
      }
      
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };
    
    loadData();
  }, [fetchDashboardData, config?.dashboardApiUrl, configLoading]);

  // Auto-refresh
  useEffect(() => {
    if (!data?.config?.auto_refresh_interval) return;

    const interval = setInterval(refreshData, data.config.auto_refresh_interval);
    return () => clearInterval(interval);
  }, [data?.config?.auto_refresh_interval]);

  // Handle sort
  const handleSort = (column: SortColumn) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Handle record selection
  const toggleRecordSelection = (recordId: number) => {
    setSelectedRecords(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recordId)) {
        newSet.delete(recordId);
      } else {
        newSet.add(recordId);
      }
      return newSet;
    });
  };

  // Select all visible records
  const toggleSelectAll = () => {
    const allVisible = paginatedData.map(record => record.id);
    const allSelected = allVisible.every(id => selectedRecords.has(id));
    
    if (allSelected) {
      setSelectedRecords(prev => {
        const newSet = new Set(prev);
        allVisible.forEach(id => newSet.delete(id));
        return newSet;
      });
    } else {
      setSelectedRecords(prev => {
        const newSet = new Set(prev);
        allVisible.forEach(id => newSet.add(id));
        return newSet;
      });
    }
  };


  // Copy link to clipboard
  const copyLink = async (link: string) => {
    try {
      await navigator.clipboard.writeText(link);
      addToast('success', t('linkCopied'));
    } catch (error) {
      addToast('error', 'Failed to copy link');
    }
  };

  // Handle edit
  const handleEdit = (record: DashboardRecord) => {
    setEditDialog({
      isOpen: true,
      record,
      issue: record.issue || '',
      description: record.description || '',
      box_link: record.box_link || ''
    });
  };

  // Handle delete
  const handleDelete = (record: DashboardRecord) => {
    setAuthDialog({
      isOpen: true,
      action: 'delete',
      record,
      password: '',
      isConfirming: false
    });
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    const records = data?.data.filter(record => selectedRecords.has(record.id)) || [];
    setAuthDialog({
      isOpen: true,
      action: 'bulkDelete',
      record: null,
      selectedRecords: records,
      password: '',
      isConfirming: false
    });
  };

  // Perform operations
  const performEdit = async () => {
    if (!editDialog.record || !authDialog.password) return;

    try {
      await axios.put(
        `${config?.dashboardApiUrl}/api/dashboard/record/${editDialog.record.id}`,
        {
          issue: editDialog.issue,
          description: editDialog.description,
          box_link: editDialog.box_link || null,
          password: authDialog.password
        }
      );

      setEditDialog({ isOpen: false, record: null, issue: '', description: '', box_link: '' });
      await refreshData();
      addToast('success', t('recordUpdated'));
    } catch (err: any) {
      throw new Error(err.response?.data?.detail || err.message || t('updateError'));
    }
  };

  const performDelete = async () => {
    if (!authDialog.record || !authDialog.password) return;

    try {
      await axios.request({
        method: 'DELETE',
        url: `${config?.dashboardApiUrl}/api/dashboard/record/${authDialog.record.id}`,
        data: { password: authDialog.password }
      });

      await refreshData();
      addToast('success', t('recordDeleted'));
    } catch (err: any) {
      throw new Error(err.response?.data?.detail || err.message || t('deleteError'));
    }
  };

  const performBulkDelete = async () => {
    if (!authDialog.selectedRecords || !authDialog.password) return;

    try {
      await Promise.all(
        authDialog.selectedRecords.map(record =>
          axios.request({
            method: 'DELETE',
            url: `${config?.dashboardApiUrl}/api/dashboard/record/${record.id}`,
            data: { password: authDialog.password }
          })
        )
      );

      setSelectedRecords(new Set());
      await refreshData();
      addToast('success', t('recordsDeleted', { count: authDialog.selectedRecords.length }));
    } catch (err: any) {
      throw new Error(err.response?.data?.detail || err.message || t('deleteError'));
    }
  };

  // Handle auth submit
  const handleAuthSubmit = async () => {
    if (!authDialog.password) return;

    try {
      setAuthDialog(prev => ({ ...prev, isConfirming: true }));

      if (authDialog.action === 'edit') {
        await performEdit();
      } else if (authDialog.action === 'delete') {
        await performDelete();
      } else if (authDialog.action === 'bulkDelete') {
        await performBulkDelete();
      }

      setAuthDialog({
        isOpen: false,
        action: 'edit',
        record: null,
        password: '',
        isConfirming: false
      });
    } catch (err: any) {
      addToast('error', err.message);
      setAuthDialog(prev => ({ ...prev, isConfirming: false }));
    }
  };

  const handleEditSubmit = () => {
    if (!editDialog.record) return;
    setAuthDialog({
      isOpen: true,
      action: 'edit',
      record: editDialog.record,
      password: '',
      isConfirming: false
    });
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return t('justNow');
    if (diffMins < 60) return t('minutesAgo', { count: diffMins });
    if (diffHours < 24) return t('hoursAgo', { count: diffHours });
    return t('daysAgo', { count: diffDays });
  };

  if (configLoading || loading) {
    return (
      <div className="enhanced-dashboard-container">
        <div className="dashboard-loading">
          <div className="loading-spinner-enhanced"></div>
          <span>{t('loading')}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="enhanced-dashboard-container">
        <div className="dashboard-error">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error}</div>
          <button onClick={refreshData} className="retry-button">
            {t('retry')}
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="enhanced-dashboard-container">
        <div className="dashboard-empty">
          {t('noData')}
        </div>
      </div>
    );
  }

  const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage);

  return (
    <div className="enhanced-dashboard-container">
      {/* Toast Notifications */}
      <div className="toast-container">
        {toasts.map(toast => (
          <div key={toast.id} className={`toast toast-${toast.type}`}>
            <span>{toast.message}</span>
            <button onClick={() => setToasts(prev => prev.filter(t => t.id !== toast.id))}>
              ×
            </button>
          </div>
        ))}
      </div>

      {/* Main Dashboard Tabs */}
      <div className="dashboard-tabs">
        <button 
          className={`tab-button ${activeTab === 'data' ? 'active' : ''}`}
          onClick={() => setActiveTab('data')}
        >
          📊 Data Management
        </button>
        <button 
          className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => setActiveTab('analytics')}
        >
          📈 Analytics
        </button>
        <button 
          className={`tab-button ${activeTab === 'activity' ? 'active' : ''}`}
          onClick={() => setActiveTab('activity')}
        >
          📝 Activity Log
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'analytics' && (
        <AnalyticsDashboard />
      )}

      {activeTab === 'activity' && (
        <div className="activity-tab-content">
          <div className="activity-header-section">
            <h2>📝 System Activity</h2>
            <p>Monitor all system activities, user actions, and system events in real-time.</p>
          </div>
          <ActivityFeed />
        </div>
      )}

      {activeTab === 'data' && (
        <div className="data-tab-content">

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-value">{statistics.total}</div>
          <div className="stat-label">{t('totalRecords')}</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">{statistics.withLinks}</div>
          <div className="stat-label">{t('withLinks')}</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">{statistics.withoutLinks}</div>
          <div className="stat-label">{t('withoutLinks')}</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">
            {lastUpdated ? formatTimeAgo(lastUpdated) : '-'}
          </div>
          <div className="stat-label">{t('lastUpdate')}</div>
        </div>
      </div>

      {/* Header */}
      <div className="dashboard-header-enhanced">
        <h2 className="dashboard-title-enhanced">{t('dashboardTitle')}</h2>
        
        <div className="header-actions">
          {selectedRecords.size > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">
                {selectedRecords.size} {t('selected', { count: selectedRecords.size })}
              </span>
              <button onClick={handleBulkDelete} className="bulk-delete-btn">
                {t('bulkDelete')}
              </button>
            </div>
          )}
          
          <div className="action-buttons">
            <BackupManager 
              onSuccess={addToast.bind(null, 'success')}
              onError={addToast.bind(null, 'error')}
            />
            <button
              onClick={refreshData}
              className={`refresh-btn-enhanced ${refreshing ? 'refreshing' : ''}`}
              disabled={refreshing}
            >
              <span className="refresh-icon">🔄</span>
              {refreshing ? t('refreshing') : t('refresh')}
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="search-filter-bar">
        <div className="search-input-container">
          <input
            type="text"
            placeholder={t('searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <span className="search-icon">🔍</span>
        </div>
        
        <div className="filter-controls">
          <span className="results-count">
            {t('showingResults', { count: filteredAndSortedData.length, total: data.total_count })}
          </span>
          {searchTerm && (
            <button onClick={() => setSearchTerm('')} className="clear-search-btn">
              {t('clearFilters')}
            </button>
          )}
        </div>
      </div>

      {/* Data Table */}
      <div className="enhanced-table">
        <div className="table-header-enhanced">
          <div className="header-cell-checkbox">
            <input
              type="checkbox"
              checked={paginatedData.length > 0 && paginatedData.every(record => selectedRecords.has(record.id))}
              onChange={toggleSelectAll}
              className="checkbox-input"
            />
          </div>
          <div 
            className={`header-cell sortable ${sortColumn === 'id' ? 'sorted' : ''}`}
            onClick={() => handleSort('id')}
          >
            {t('id')}
            {sortColumn === 'id' && (
              <span className={`sort-indicator ${sortDirection}`}>
                {sortDirection === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </div>
          <div 
            className={`header-cell sortable ${sortColumn === 'date' ? 'sorted' : ''}`}
            onClick={() => handleSort('date')}
          >
            Date
            {sortColumn === 'date' && (
              <span className={`sort-indicator ${sortDirection}`}>
                {sortDirection === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </div>
          <div 
            className={`header-cell sortable ${sortColumn === 'issue' ? 'sorted' : ''}`}
            onClick={() => handleSort('issue')}
          >
            Issue
            {sortColumn === 'issue' && (
              <span className={`sort-indicator ${sortDirection}`}>
                {sortDirection === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </div>
          <div 
            className={`header-cell sortable ${sortColumn === 'description' ? 'sorted' : ''}`}
            onClick={() => handleSort('description')}
          >
            Description
            {sortColumn === 'description' && (
              <span className={`sort-indicator ${sortDirection}`}>
                {sortDirection === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </div>
          <div 
            className={`header-cell sortable ${sortColumn === 'box_link' ? 'sorted' : ''}`}
            onClick={() => handleSort('box_link')}
          >
            {t('boxLink')}
            {sortColumn === 'box_link' && (
              <span className={`sort-indicator ${sortDirection}`}>
                {sortDirection === 'asc' ? '↑' : '↓'}
              </span>
            )}
          </div>
          <div className="header-cell">{t('actions')}</div>
        </div>

        <div className="table-body-enhanced">
          {paginatedData.map((record) => (
            <div key={record.id} className="table-row-enhanced">
              <div className="table-cell-checkbox">
                <input
                  type="checkbox"
                  checked={selectedRecords.has(record.id)}
                  onChange={() => toggleRecordSelection(record.id)}
                  className="checkbox-input"
                />
              </div>
              <div className="table-cell cell-id-enhanced">{record.id}</div>
              <div className="table-cell cell-date-enhanced">
                <div className="date-content-enhanced">
                  {record.date || 'No date'}
                </div>
              </div>
              <div className="table-cell cell-issue-enhanced">
                <div className="issue-content-enhanced">
                  {record.issue && record.issue.length > 60 
                    ? `${record.issue.substring(0, 60)}...` 
                    : record.issue || 'No issue'}
                </div>
              </div>
              <div className="table-cell cell-description-enhanced">
                <div className="description-content-enhanced">
                  {record.description && record.description.length > 100 
                    ? `${record.description.substring(0, 100)}...` 
                    : record.description || 'No description'}
                </div>
              </div>
              <div className="table-cell cell-link-enhanced">
                {record.box_link ? (
                  <div className="link-actions">
                    <a 
                      href={record.box_link} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="box-link-enhanced"
                    >
                      {t('viewFile')}
                    </a>
                    <button 
                      onClick={() => copyLink(record.box_link!)}
                      className="copy-link-btn"
                      title={t('copyLink')}
                    >
                      📋
                    </button>
                  </div>
                ) : (
                  <span className="no-link-enhanced">{t('noLink')}</span>
                )}
              </div>
              <div className="table-cell cell-actions-enhanced">
                <button
                  onClick={() => handleEdit(record)}
                  className="action-button-enhanced edit-button-enhanced"
                >
                  {t('edit')}
                </button>
                <button
                  onClick={() => handleDelete(record)}
                  className="action-button-enhanced delete-button-enhanced"
                >
                  {t('delete')}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination-enhanced">
          <div className="pagination-info">
            {t('showing')} {((currentPage - 1) * itemsPerPage) + 1} {t('to')} {Math.min(currentPage * itemsPerPage, filteredAndSortedData.length)} {t('of')} {filteredAndSortedData.length}
          </div>
          
          <div className="pagination-controls">
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="pagination-btn"
            >
              ←
            </button>
            
            <span className="page-info">
              {t('page')} {currentPage} {t('of')} {totalPages}
            </span>
            
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="pagination-btn"
            >
              →
            </button>
          </div>

          <div className="items-per-page">
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="items-per-page-select"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span>{t('itemsPerPage')}</span>
          </div>
        </div>
      )}

      {/* Edit Dialog */}
      {editDialog.isOpen && (
        <div className="dialog-overlay-enhanced">
          <div className="dialog-enhanced">
            <div className="dialog-header-enhanced">
              <h3>{t('editRecord', { id: editDialog.record?.id })}</h3>
              <button
                onClick={() => setEditDialog({ isOpen: false, record: null, issue: '', description: '', box_link: '' })}
                className="dialog-close-enhanced"
              >
                ×
              </button>
            </div>
            <div className="dialog-body-enhanced">
              <div className="form-group-enhanced">
                <label>Issue</label>
                <input
                  type="text"
                  value={editDialog.issue}
                  onChange={(e) => setEditDialog(prev => ({ ...prev, issue: e.target.value }))}
                  className="edit-input-enhanced"
                  placeholder="Enter issue summary..."
                />
              </div>
              <div className="form-group-enhanced">
                <label>Description</label>
                <textarea
                  value={editDialog.description}
                  onChange={(e) => setEditDialog(prev => ({ ...prev, description: e.target.value }))}
                  className="edit-textarea-enhanced"
                  rows={4}
                  placeholder="Enter detailed description..."
                />
              </div>
              <div className="form-group-enhanced">
                <label>{t('boxLinkLabel')}</label>
                <input
                  type="url"
                  value={editDialog.box_link}
                  onChange={(e) => setEditDialog(prev => ({ ...prev, box_link: e.target.value }))}
                  className="edit-input-enhanced"
                  placeholder="https://..."
                />
              </div>
            </div>
            <div className="dialog-footer-enhanced">
              <button
                onClick={() => setEditDialog({ isOpen: false, record: null, queries: '', box_link: '' })}
                className="dialog-button-enhanced cancel-button-enhanced"
              >
                {t('cancel')}
              </button>
              <button
                onClick={handleEditSubmit}
                className="dialog-button-enhanced save-button-enhanced"
              >
                {t('save')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Dialog */}
      {authDialog.isOpen && (
        <div className="dialog-overlay-enhanced">
          <div className="dialog-enhanced auth-dialog-enhanced">
            <div className="dialog-header-enhanced">
              <h3>{t('authRequired')}</h3>
              <button
                onClick={() => setAuthDialog({ isOpen: false, action: 'edit', record: null, password: '', isConfirming: false })}
                className="dialog-close-enhanced"
              >
                ×
              </button>
            </div>
            <div className="dialog-body-enhanced">
              <p>
                {authDialog.action === 'edit' 
                  ? t('confirmAction', { action: t('edit'), id: authDialog.record?.id })
                  : authDialog.action === 'delete'
                  ? t('confirmAction', { action: t('delete'), id: authDialog.record?.id })
                  : t('confirmBulkAction', { count: authDialog.selectedRecords?.length || 0 })
                }
              </p>
              <div className="form-group-enhanced">
                <label>{t('enterPassword')}</label>
                <input
                  type="password"
                  value={authDialog.password}
                  onChange={(e) => setAuthDialog(prev => ({ ...prev, password: e.target.value }))}
                  className="auth-input-enhanced"
                  placeholder={t('password')}
                  disabled={authDialog.isConfirming}
                />
              </div>
            </div>
            <div className="dialog-footer-enhanced">
              <button
                onClick={() => setAuthDialog({ isOpen: false, action: 'edit', record: null, password: '', isConfirming: false })}
                className="dialog-button-enhanced cancel-button-enhanced"
                disabled={authDialog.isConfirming}
              >
                {t('cancel')}
              </button>
              <button
                onClick={handleAuthSubmit}
                className={`dialog-button-enhanced confirm-button-enhanced ${authDialog.action}`}
                disabled={!authDialog.password || authDialog.isConfirming}
              >
                {authDialog.isConfirming 
                  ? t('processing')
                  : authDialog.action === 'edit' 
                    ? t('confirmEdit')
                    : authDialog.action === 'delete'
                    ? t('confirmDeleteAction')
                    : t('confirmDeleteAction')
                }
              </button>
            </div>
          </div>
        </div>
      )}

        </div>
      )}
    </div>
  );
};

export default EnhancedDashboard;