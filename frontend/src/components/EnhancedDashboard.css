/* Enhanced Dashboard Container */
.enhanced-dashboard-container {
  width: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: 'Inter', 'Noto Sans JP', sans-serif;
  font-feature-settings: 'liga' 1, 'calt' 1;
  letter-spacing: -0.01em;
}


@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.96) translateY(24px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  background: white;
  border-radius: 12px 12px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-bottom: none;
  margin: 24px 0 0 0;
  overflow-x: auto;
}

.tab-button {
  background: none;
  border: none;
  padding: 16px 24px;
  cursor: pointer;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 3px solid transparent;
  transition: all 0.2s;
  white-space: nowrap;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tab-button:hover {
  color: #374151;
  background: #f9fafb;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

/* Tab Content */
.data-tab-content,
.activity-tab-content {
  background: white;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  border-top: none;
  overflow: hidden;
  margin-bottom: 24px;
}

.activity-header-section {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  margin: 0;
}

.activity-header-section h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #111827;
}

.activity-header-section p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 100px;
  right: 32px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toast {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  animation: slideInToast 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
  font-size: 14px;
  min-width: 320px;
  max-width: 400px;
}

.toast-success {
  background: rgba(16, 185, 129, 0.95);
  color: white;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.toast-error {
  background: rgba(239, 68, 68, 0.95);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.toast-info {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.toast button {
  background: none;
  border: none;
  color: inherit;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin-left: 16px;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.toast button:hover {
  opacity: 1;
}

@keyframes slideInToast {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin: 24px 0 32px 0;
  padding: 0 24px;
}

.stat-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #6366f1, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
  letter-spacing: -0.01em;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Header */
.dashboard-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 24px 0 32px 0;
  padding: 0 24px;
  gap: 24px;
}

.dashboard-title-enhanced {
  font-size: clamp(28px, 4vw, 36px);
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.header-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16px;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border: 1px solid #f59e0b;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
}

.selected-count {
  color: #92400e;
  font-weight: 600;
}

.bulk-delete-btn,
.export-selected-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.bulk-delete-btn {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.bulk-delete-btn:hover {
  background: #fecaca;
  transform: translateY(-1px);
}

.export-selected-btn {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #bfdbfe;
}

.export-selected-btn:hover {
  background: #bfdbfe;
  transform: translateY(-1px);
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.export-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  font-size: 14px;
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.refresh-btn-enhanced {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  font-size: 14px;
}

.refresh-btn-enhanced:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.refresh-btn-enhanced:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.refresh-btn-enhanced.refreshing .refresh-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Search and Filter Bar */
.search-filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 24px 0;
  padding: 0 24px;
  gap: 20px;
}

.search-input-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 14px 48px 14px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: #ffffff;
  color: #374151;
  font-family: inherit;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.search-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #64748b;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.results-count {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.clear-search-btn {
  padding: 8px 16px;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
}

.clear-search-btn:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

/* Enhanced Table */
.enhanced-table {
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin: 0 24px 24px 24px;
}

.table-header-enhanced {
  display: grid;
  grid-template-columns: 60px 100px 1fr 160px 160px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 2px solid #e2e8f0;
}

.header-cell {
  padding: 18px 16px;
  font-weight: 700;
  color: #374151;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-right: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell-checkbox {
  padding: 18px 16px;
  border-right: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-cell.sortable {
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.header-cell.sortable:hover {
  background: rgba(59, 130, 246, 0.05);
  color: #3b82f6;
}

.header-cell.sorted {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.08);
}

.sort-indicator {
  font-size: 14px;
  font-weight: 800;
}

.table-body-enhanced {
  max-height: 70vh;
  overflow-y: auto;
}

.table-row-enhanced {
  display: grid;
  grid-template-columns: 60px 100px 1fr 160px 160px;
  border-bottom: 1px solid #f8fafc;
  transition: all 0.2s ease;
}

.table-row-enhanced:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: scale(1.002);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.table-row-enhanced:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 20px 16px;
  border-right: 1px solid #f8fafc;
  display: flex;
  align-items: center;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.5;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell-checkbox {
  padding: 20px 16px;
  border-right: 1px solid #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #3b82f6;
}

.cell-id-enhanced {
  font-weight: 700;
  color: #3b82f6;
  justify-content: center;
  font-size: 15px;
}

.queries-content-enhanced {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  font-weight: 400;
}

.cell-link-enhanced .link-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.box-link-enhanced {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 8px;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  transition: all 0.2s ease;
  font-size: 12px;
}

.box-link-enhanced:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.copy-link-btn {
  padding: 6px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.copy-link-btn:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

.no-link-enhanced {
  color: #9ca3af;
  font-size: 13px;
  font-style: italic;
  font-weight: 400;
}

.cell-actions-enhanced {
  gap: 8px;
  justify-content: center;
}

.action-button-enhanced {
  padding: 8px 14px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-family: inherit;
}

.edit-button-enhanced {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  color: #92400e;
  border: 1px solid #f59e0b;
}

.edit-button-enhanced:hover {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.delete-button-enhanced {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #991b1b;
  border: 1px solid #f87171;
}

.delete-button-enhanced:hover {
  background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(248, 113, 113, 0.3);
}

/* Pagination */
.pagination-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-top: 1px solid #f1f5f9;
  margin: 24px 0 0 0;
}

.pagination-info {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.pagination-btn {
  padding: 10px 16px;
  background: #ffffff;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.pagination-btn:hover:not(:disabled) {
  border-color: #3b82f6;
  color: #3b82f6;
  transform: translateY(-1px);
}

.pagination-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.page-info {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.items-per-page-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  font-size: 14px;
  cursor: pointer;
}

/* Loading States */
.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 20px;
  color: #64748b;
}

.loading-spinner-enhanced {
  width: 48px;
  height: 48px;
  border: 5px solid #f1f5f9;
  border-top: 5px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120px 20px;
  text-align: center;
}

.error-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.error-message {
  color: #dc2626;
  font-weight: 600;
  margin-bottom: 32px;
  max-width: 500px;
  font-size: 16px;
  line-height: 1.6;
}

.retry-button {
  padding: 14px 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
}

.retry-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.dashboard-empty {
  text-align: center;
  color: #64748b;
  padding: 120px 20px;
  font-size: 20px;
  font-weight: 500;
}

/* Enhanced Dialog Styles */
.dialog-overlay-enhanced {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.65);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.dialog-enhanced {
  background: white;
  border-radius: 20px;
  box-shadow: 
    0 24px 80px rgba(0, 0, 0, 0.25),
    0 8px 32px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 85vh;
  overflow: hidden;
  animation: slideUpDialog 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideUpDialog {
  from {
    opacity: 0;
    transform: translateY(60px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header-enhanced {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28px 32px 0;
  border-bottom: 1px solid #f1f5f9;
  margin-bottom: 32px;
}

.dialog-header-enhanced h3 {
  margin: 0;
  font-size: 22px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -0.01em;
}

.dialog-close-enhanced {
  background: none;
  border: none;
  font-size: 28px;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  transition: all 0.2s ease;
}

.dialog-close-enhanced:hover {
  background: #f1f5f9;
  color: #374151;
}

.dialog-body-enhanced {
  padding: 0 32px 32px;
}

.form-group-enhanced {
  margin-bottom: 24px;
}

.form-group-enhanced label {
  display: block;
  font-weight: 700;
  color: #374151;
  margin-bottom: 10px;
  font-size: 15px;
  letter-spacing: -0.01em;
}

.edit-textarea-enhanced,
.edit-input-enhanced,
.auth-input-enhanced {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  transition: all 0.3s ease;
  box-sizing: border-box;
  font-family: inherit;
  font-weight: 400;
  line-height: 1.5;
}

.edit-textarea-enhanced {
  resize: vertical;
  min-height: 120px;
}

.edit-textarea-enhanced:focus,
.edit-input-enhanced:focus,
.auth-input-enhanced:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.dialog-footer-enhanced {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 32px;
  border-top: 1px solid #f1f5f9;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.dialog-button-enhanced {
  padding: 14px 28px;
  border: none;
  border-radius: 10px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  font-family: inherit;
}

.cancel-button-enhanced {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #e2e8f0;
}

.cancel-button-enhanced:hover:not(:disabled) {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.save-button-enhanced {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.save-button-enhanced:hover:not(:disabled) {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.confirm-button-enhanced {
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.confirm-button-enhanced.edit {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.confirm-button-enhanced.edit:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  transform: translateY(-1px);
}

.confirm-button-enhanced.delete,
.confirm-button-enhanced.bulkDelete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.confirm-button-enhanced.delete:hover:not(:disabled),
.confirm-button-enhanced.bulkDelete:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
}

.dialog-button-enhanced:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.auth-dialog-enhanced .dialog-body-enhanced p {
  margin-bottom: 24px;
  color: #475569;
  line-height: 1.7;
  font-size: 15px;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .enhanced-dashboard-container {
    padding: 28px;
  }
  
  .table-header-enhanced,
  .table-row-enhanced {
    grid-template-columns: 50px 90px 1fr 140px 140px;
  }
}

@media (max-width: 1200px) {
  .enhanced-dashboard-container {
    padding: 24px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 16px;
  }
  
  .stat-value {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .enhanced-dashboard-container {
    padding: 16px;
    margin: 0 8px;
  }
  
  .dashboard-header-enhanced {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .header-actions {
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: stretch;
  }
  
  .export-btn,
  .refresh-btn-enhanced {
    flex: 1;
    justify-content: center;
  }
  
  .search-filter-bar {
    flex-direction: column;
    gap: 16px;
  }
  
  .search-input-container {
    max-width: none;
  }
  
  .table-header-enhanced,
  .table-row-enhanced {
    grid-template-columns: 40px 80px 1fr 100px;
  }
  
  .header-cell:nth-child(4),
  .table-cell:nth-child(4) {
    display: none;
  }
  
  .header-cell:nth-child(5),
  .table-cell:nth-child(5) {
    grid-column: 4;
  }
  
  .cell-actions-enhanced {
    flex-direction: column;
    gap: 6px;
  }
  
  .action-button-enhanced {
    font-size: 11px;
    padding: 6px 10px;
  }
  
  .dialog-enhanced {
    width: 95%;
    margin: 20px;
  }
  
  .dialog-header-enhanced,
  .dialog-body-enhanced,
  .dialog-footer-enhanced {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .dialog-footer-enhanced {
    flex-direction: column-reverse;
  }
  
  .dialog-button-enhanced {
    width: 100%;
  }
  
  .pagination-enhanced {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .dashboard-title-enhanced {
    font-size: 24px;
  }
  
  .table-header-enhanced,
  .table-row-enhanced {
    grid-template-columns: 35px 70px 1fr 90px;
  }
  
  .table-cell {
    padding: 16px 8px;
    font-size: 13px;
  }
  
  .queries-content-enhanced {
    font-size: 13px;
  }
  
  .stat-value {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 0.75rem;
  }
}