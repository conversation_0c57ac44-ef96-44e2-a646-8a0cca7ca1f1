.activity-feed {
  background: white;
  border-radius: 0;
  box-shadow: none;
  border: none;
  overflow: hidden;
  margin: 0;
}

/* Standalone activity feed with full styling */
.activity-feed.standalone {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin: 24px;
}

.activity-feed.compact {
  max-height: 400px;
}

.activity-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fafb;
}

.activity-header h3 {
  margin: 0;
  font-size: 18px;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 8px;
}

.activity-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-updated-small {
  font-size: 12px;
  color: #6b7280;
}

.refresh-btn-small {
  background: none;
  border: 1px solid #d1d5db;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: all 0.2s;
}

.refresh-btn-small:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.activity-list {
  padding: 16px 20px;
  max-height: 600px;
  overflow-y: auto;
}

.activity-feed.compact .activity-list {
  max-height: 300px;
}

.activity-item {
  display: flex;
  gap: 16px;
  position: relative;
  padding-bottom: 16px;
}

.activity-item:last-child {
  padding-bottom: 0;
}

.activity-item.activity-error {
  background: rgba(254, 226, 226, 0.3);
  margin: -8px -12px 8px -12px;
  padding: 8px 12px 16px 12px;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

.activity-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.activity-dot {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.activity-icon {
  font-size: 14px;
  filter: brightness(0) invert(1);
}

.activity-line {
  width: 2px;
  flex: 1;
  background: #e5e7eb;
  margin: 8px 0;
  min-height: 20px;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.activity-description {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

.activity-time {
  font-size: 12px;
  color: #9ca3af;
  white-space: nowrap;
  flex-shrink: 0;
}

.activity-metadata {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.activity-ip {
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.activity-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.activity-status.error {
  background: #fee2e2;
  color: #dc2626;
}

.activity-footer {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-summary {
  font-size: 12px;
  color: #6b7280;
}

.load-more-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.load-more-btn:hover {
  background: #2563eb;
}

/* Loading and Error States */
.activity-loading, .activity-error {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner-small {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

.retry-btn-small {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 8px;
}

.retry-btn-small:hover {
  background: #2563eb;
}

.no-activities {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-activities-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.no-activities p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .activity-header {
    padding: 16px;
  }
  
  .activity-list {
    padding: 12px 16px;
  }
  
  .activity-main {
    flex-direction: column;
    gap: 4px;
  }
  
  .activity-time {
    align-self: flex-start;
  }
  
  .activity-metadata {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .activity-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
  }
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.activity-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar Styling */
.activity-list::-webkit-scrollbar {
  width: 6px;
}

.activity-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}