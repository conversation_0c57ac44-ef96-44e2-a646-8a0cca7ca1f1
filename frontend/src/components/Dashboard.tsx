import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import './Dashboard.css';

interface DashboardRecord {
  id: number;
  date: string | null;
  issue: string | null;
  description: string | null;
  metadata: string | null;
  queries: string | null;
  box_link: string | null;
  source: string | null;
  created_at: string;
  updated_at: string;
}

interface DashboardData {
  data: DashboardRecord[];
  total_count: number;
  config: {
    items_per_page: number;
    auto_refresh_interval: number;
    theme: string;
    enable_animations: boolean;
  };
}

interface EditDialogState {
  isOpen: boolean;
  record: DashboardRecord | null;
  queries: string;
  box_link: string;
}

interface AuthDialogState {
  isOpen: boolean;
  action: 'edit' | 'delete';
  record: DashboardRecord | null;
  password: string;
  isConfirming: boolean;
}

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Dialog states
  const [editDialog, setEditDialog] = useState<EditDialogState>({
    isOpen: false,
    record: null,
    queries: '',
    box_link: ''
  });
  
  const [authDialog, setAuthDialog] = useState<AuthDialogState>({
    isOpen: false,
    action: 'edit',
    record: null,
    password: '',
    isConfirming: false
  });

  const dashboardApiUrl = process.env.REACT_APP_DASHBOARD_API_URL;

  const fetchDashboardData = useCallback(async () => {
    try {
      if (!dashboardApiUrl) {
        throw new Error('Backend URL not configured');
      }

      const response = await axios.get(`${dashboardApiUrl}/api/dashboard`);
      setData(response.data);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Failed to fetch dashboard data');
      console.error('Dashboard data fetch error:', err);
    }
  }, [dashboardApiUrl]);

  const refreshData = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchDashboardData();
      setLoading(false);
    };
    
    loadData();
  }, [fetchDashboardData]);

  // Auto-refresh setup
  useEffect(() => {
    if (!data?.config?.auto_refresh_interval) return;

    const interval = setInterval(refreshData, data.config.auto_refresh_interval);
    return () => clearInterval(interval);
  }, [data?.config?.auto_refresh_interval, refreshData]);

  const handleEdit = (record: DashboardRecord) => {
    setEditDialog({
      isOpen: true,
      record,
      queries: record.queries,
      box_link: record.box_link || ''
    });
  };

  const handleDelete = (record: DashboardRecord) => {
    setAuthDialog({
      isOpen: true,
      action: 'delete',
      record,
      password: '',
      isConfirming: false
    });
  };

  const handleAuthSubmit = async () => {
    if (!authDialog.record || !authDialog.password) return;

    try {
      setAuthDialog(prev => ({ ...prev, isConfirming: true }));

      if (authDialog.action === 'edit') {
        // Proceed with edit
        await performEdit();
      } else if (authDialog.action === 'delete') {
        // Proceed with delete
        await performDelete();
      }

      setAuthDialog({
        isOpen: false,
        action: 'edit',
        record: null,
        password: '',
        isConfirming: false
      });
    } catch (err: any) {
      setError(err.response?.data?.detail || err.message || 'Authentication failed');
      setAuthDialog(prev => ({ ...prev, isConfirming: false }));
    }
  };

  const performEdit = async () => {
    if (!editDialog.record || !authDialog.password) return;

    try {
      await axios.put(
        `${dashboardApiUrl}/api/dashboard/record/${editDialog.record.id}`,
        {
          queries: editDialog.queries,
          box_link: editDialog.box_link || null,
          password: authDialog.password
        }
      );

      setEditDialog({
        isOpen: false,
        record: null,
        queries: '',
        box_link: ''
      });

      await refreshData();
    } catch (err: any) {
      throw new Error(err.response?.data?.detail || err.message || 'Failed to update record');
    }
  };

  const performDelete = async () => {
    if (!authDialog.record || !authDialog.password) return;

    try {
      await axios.request({
        method: 'DELETE',
        url: `${dashboardApiUrl}/api/dashboard/record/${authDialog.record.id}`,
        data: {
          password: authDialog.password
        }
      });

      await refreshData();
    } catch (err: any) {
      throw new Error(err.response?.data?.detail || err.message || 'Failed to delete record');
    }
  };

  const handleEditSubmit = () => {
    if (!editDialog.record) return;

    setAuthDialog({
      isOpen: true,
      action: 'edit',
      record: editDialog.record,
      password: '',
      isConfirming: false
    });
  };

  if (loading) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-loading">
          <div className="loading-spinner"></div>
          <span>Loading dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-error">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error}</div>
          <button onClick={refreshData} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="dashboard-container">
        <div className="dashboard-empty">
          No data available
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h2 className="dashboard-title">Data Dashboard</h2>
        <div className="dashboard-controls">
          <span className="record-count">
            {data.total_count} records
          </span>
          <button
            onClick={refreshData}
            className={`refresh-button ${refreshing ? 'refreshing' : ''}`}
            disabled={refreshing}
          >
            <span className="refresh-icon">🔄</span>
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      <div className="dashboard-table">
        <div className="table-header">
          <div className="header-cell header-id">ID</div>
          <div className="header-cell header-date">Date</div>
          <div className="header-cell header-issue">Issue</div>
          <div className="header-cell header-description">Description</div>
          <div className="header-cell header-link">Box Link</div>
          <div className="header-cell header-actions">Actions</div>
        </div>

        <div className="table-body">
          {data.data.map((record) => (
            <div key={record.id} className="table-row">
              <div className="table-cell cell-id">{record.id}</div>
              <div className="table-cell cell-date">
                <div className="date-content">
                  {record.date || 'No date'}
                </div>
              </div>
              <div className="table-cell cell-issue">
                <div className="issue-content">
                  {record.issue && record.issue.length > 50 
                    ? `${record.issue.substring(0, 50)}...` 
                    : record.issue || 'No issue'}
                </div>
              </div>
              <div className="table-cell cell-description">
                <div className="description-content">
                  {record.description && record.description.length > 80 
                    ? `${record.description.substring(0, 80)}...` 
                    : record.description || 'No description'}
                </div>
              </div>
              <div className="table-cell cell-link">
                {record.box_link ? (
                  <a 
                    href={record.box_link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="box-link"
                  >
                    View File
                  </a>
                ) : (
                  <span className="no-link">No link</span>
                )}
              </div>
              <div className="table-cell cell-actions">
                <button
                  onClick={() => handleEdit(record)}
                  className="action-button edit-button"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(record)}
                  className="action-button delete-button"
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Edit Dialog */}
      {editDialog.isOpen && (
        <div className="dialog-overlay">
          <div className="dialog">
            <div className="dialog-header">
              <h3>Edit Record #{editDialog.record?.id}</h3>
              <button
                onClick={() => setEditDialog({ isOpen: false, record: null, queries: '', box_link: '' })}
                className="dialog-close"
              >
                ×
              </button>
            </div>
            <div className="dialog-body">
              <div className="form-group">
                <label>Queries:</label>
                <textarea
                  value={editDialog.queries}
                  onChange={(e) => setEditDialog(prev => ({ ...prev, queries: e.target.value }))}
                  className="edit-textarea"
                  rows={4}
                />
              </div>
              <div className="form-group">
                <label>Box Link:</label>
                <input
                  type="url"
                  value={editDialog.box_link}
                  onChange={(e) => setEditDialog(prev => ({ ...prev, box_link: e.target.value }))}
                  className="edit-input"
                  placeholder="https://..."
                />
              </div>
            </div>
            <div className="dialog-footer">
              <button
                onClick={() => setEditDialog({ isOpen: false, record: null, queries: '', box_link: '' })}
                className="dialog-button cancel-button"
              >
                Cancel
              </button>
              <button
                onClick={handleEditSubmit}
                className="dialog-button save-button"
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Authentication Dialog */}
      {authDialog.isOpen && (
        <div className="dialog-overlay">
          <div className="dialog auth-dialog">
            <div className="dialog-header">
              <h3>Authentication Required</h3>
              <button
                onClick={() => setAuthDialog({ isOpen: false, action: 'edit', record: null, password: '', isConfirming: false })}
                className="dialog-close"
              >
                ×
              </button>
            </div>
            <div className="dialog-body">
              <p>
                {authDialog.action === 'edit' 
                  ? `Are you sure you want to edit record #${authDialog.record?.id}?`
                  : `Are you sure you want to delete record #${authDialog.record?.id}?`
                }
              </p>
              <div className="form-group">
                <label>Enter password to confirm:</label>
                <input
                  type="password"
                  value={authDialog.password}
                  onChange={(e) => setAuthDialog(prev => ({ ...prev, password: e.target.value }))}
                  className="auth-input"
                  placeholder="Password"
                  disabled={authDialog.isConfirming}
                />
              </div>
            </div>
            <div className="dialog-footer">
              <button
                onClick={() => setAuthDialog({ isOpen: false, action: 'edit', record: null, password: '', isConfirming: false })}
                className="dialog-button cancel-button"
                disabled={authDialog.isConfirming}
              >
                Cancel
              </button>
              <button
                onClick={handleAuthSubmit}
                className={`dialog-button confirm-button ${authDialog.action}`}
                disabled={!authDialog.password || authDialog.isConfirming}
              >
                {authDialog.isConfirming 
                  ? 'Processing...' 
                  : authDialog.action === 'edit' 
                    ? 'Confirm Edit' 
                    : 'Confirm Delete'
                }
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;