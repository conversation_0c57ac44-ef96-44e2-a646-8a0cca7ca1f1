import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import './BackupManager.css';

interface BackupManagerProps {
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const BackupManager: React.FC<BackupManagerProps> = ({ onSuccess, onError }) => {
  const { t } = useTranslation();
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [password, setPassword] = useState('');
  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv');
  const [isProcessing, setIsProcessing] = useState(false);

  const dashboardApiUrl = process.env.REACT_APP_DASHBOARD_API_URL;

  const handleBackup = async () => {
    if (!password.trim()) {
      onError?.('Password is required');
      return;
    }

    setIsProcessing(true);
    try {
      const response = await axios.post(`${dashboardApiUrl}/api/dashboard/backup`, {
        password: password
      });

      const data = response.data;
      onSuccess?.(`Backup created successfully: ${data.filename} (${data.record_count} records)`);
      setIsBackupModalOpen(false);
      setPassword('');
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || 'Backup failed';
      onError?.(errorMsg);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExport = async () => {
    if (!password.trim()) {
      onError?.('Password is required');
      return;
    }

    setIsProcessing(true);
    try {
      const response = await axios.get(
        `${dashboardApiUrl}/api/dashboard/export?format=${exportFormat}&auth_password=${encodeURIComponent(password)}`
      );

      const data = response.data;
      
      // Create and download file
      const blob = new Blob([data.content], { 
        type: exportFormat === 'csv' ? 'text/csv' : 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = data.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      onSuccess?.(`Data exported successfully: ${data.filename} (${data.record_count} records)`);
      setIsExportModalOpen(false);
      setPassword('');
    } catch (err: any) {
      const errorMsg = err.response?.data?.detail || err.message || 'Export failed';
      onError?.(errorMsg);
    } finally {
      setIsProcessing(false);
    }
  };

  const closeModals = () => {
    setIsBackupModalOpen(false);
    setIsExportModalOpen(false);
    setPassword('');
    setIsProcessing(false);
  };

  return (
    <div className="backup-manager">
      {/* Backup & Export Buttons */}
      <div className="backup-controls">
        <button 
          onClick={() => setIsBackupModalOpen(true)}
          className="backup-btn"
          title="Create system backup"
        >
          💾 Create Backup
        </button>
        
        <button 
          onClick={() => setIsExportModalOpen(true)}
          className="export-btn"
          title="Export data"
        >
          📤 Export Data
        </button>
      </div>

      {/* Backup Modal */}
      {isBackupModalOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>💾 Create System Backup</h3>
              <button onClick={closeModals} className="modal-close">×</button>
            </div>
            
            <div className="modal-body">
              <div className="backup-info">
                <div className="info-card">
                  <div className="info-icon">📋</div>
                  <div className="info-content">
                    <h4>What's included:</h4>
                    <ul>
                      <li>All dashboard records</li>
                      <li>Box.com links</li>
                      <li>Metadata and timestamps</li>
                      <li>Complete database snapshot</li>
                    </ul>
                  </div>
                </div>
                
                <div className="info-card">
                  <div className="info-icon">🔒</div>
                  <div className="info-content">
                    <h4>Security:</h4>
                    <p>Backup files are stored securely and can be used for system recovery or data migration.</p>
                  </div>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="backup-password">Administrator Password</label>
                <input
                  id="backup-password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password to create backup"
                  className="password-input"
                  disabled={isProcessing}
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                onClick={closeModals} 
                className="cancel-btn"
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button 
                onClick={handleBackup}
                className="confirm-btn backup"
                disabled={!password.trim() || isProcessing}
              >
                {isProcessing ? (
                  <>
                    <div className="processing-spinner"></div>
                    Creating...
                  </>
                ) : (
                  '💾 Create Backup'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Export Modal */}
      {isExportModalOpen && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>📤 Export Dashboard Data</h3>
              <button onClick={closeModals} className="modal-close">×</button>
            </div>
            
            <div className="modal-body">
              <div className="export-format-selection">
                <h4>Choose Export Format:</h4>
                <div className="format-options">
                  <label className={`format-option ${exportFormat === 'csv' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="format"
                      value="csv"
                      checked={exportFormat === 'csv'}
                      onChange={(e) => setExportFormat(e.target.value as 'csv')}
                    />
                    <div className="format-card">
                      <div className="format-icon">📊</div>
                      <div className="format-info">
                        <h5>CSV Format</h5>
                        <p>Comma-separated values, perfect for Excel and data analysis</p>
                        <div className="format-features">
                          <span>✓ Excel compatible</span>
                          <span>✓ Easy to read</span>
                          <span>✓ Lightweight</span>
                        </div>
                      </div>
                    </div>
                  </label>
                  
                  <label className={`format-option ${exportFormat === 'json' ? 'selected' : ''}`}>
                    <input
                      type="radio"
                      name="format"
                      value="json"
                      checked={exportFormat === 'json'}
                      onChange={(e) => setExportFormat(e.target.value as 'json')}
                    />
                    <div className="format-card">
                      <div className="format-icon">🔧</div>
                      <div className="format-info">
                        <h5>JSON Format</h5>
                        <p>Structured data format, ideal for developers and APIs</p>
                        <div className="format-features">
                          <span>✓ Structured data</span>
                          <span>✓ API compatible</span>
                          <span>✓ Preserves types</span>
                        </div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="export-password">Administrator Password</label>
                <input
                  id="export-password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter password to export data"
                  className="password-input"
                  disabled={isProcessing}
                />
              </div>
            </div>
            
            <div className="modal-footer">
              <button 
                onClick={closeModals} 
                className="cancel-btn"
                disabled={isProcessing}
              >
                Cancel
              </button>
              <button 
                onClick={handleExport}
                className="confirm-btn export"
                disabled={!password.trim() || isProcessing}
              >
                {isProcessing ? (
                  <>
                    <div className="processing-spinner"></div>
                    Exporting...
                  </>
                ) : (
                  `📤 Export as ${exportFormat.toUpperCase()}`
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BackupManager;