.backup-manager {
  position: relative;
}

.backup-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.backup-btn, .export-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.backup-btn {
  background: #059669;
  color: white;
}

.backup-btn:hover {
  background: #047857;
  transform: translateY(-1px);
}

.export-btn {
  background: #8b5cf6;
  color: white;
}

.export-btn:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.2s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Backup Info Cards */
.backup-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.info-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 16px;
  display: flex;
  gap: 12px;
}

.info-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.info-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

.info-content ul {
  margin: 0;
  padding-left: 16px;
  font-size: 12px;
  color: #6b7280;
}

.info-content ul li {
  margin-bottom: 4px;
}

.info-content p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* Export Format Selection */
.export-format-selection {
  margin-bottom: 24px;
}

.export-format-selection h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #374151;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.format-option {
  cursor: pointer;
  display: block;
}

.format-option input[type="radio"] {
  display: none;
}

.format-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  gap: 16px;
  transition: all 0.2s;
  background: white;
}

.format-option.selected .format-card {
  border-color: #3b82f6;
  background: #eff6ff;
}

.format-option:hover .format-card {
  border-color: #9ca3af;
}

.format-option.selected:hover .format-card {
  border-color: #2563eb;
}

.format-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.format-info h5 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #111827;
}

.format-info p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.format-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.format-features span {
  background: #f3f4f6;
  color: #059669;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.format-option.selected .format-features span {
  background: #dbeafe;
  color: #2563eb;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.password-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.password-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Modal Footer Buttons */
.cancel-btn, .confirm-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.cancel-btn:hover:not(:disabled) {
  background: #e5e7eb;
}

.confirm-btn {
  color: white;
}

.confirm-btn.backup {
  background: #059669;
}

.confirm-btn.backup:hover:not(:disabled) {
  background: #047857;
}

.confirm-btn.export {
  background: #8b5cf6;
}

.confirm-btn.export:hover:not(:disabled) {
  background: #7c3aed;
}

.confirm-btn:disabled, .cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Processing Spinner */
.processing-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 16px;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header, .modal-body {
    padding: 20px 16px;
  }
  
  .modal-footer {
    padding: 12px 16px 20px;
    flex-direction: column;
  }
  
  .backup-info {
    grid-template-columns: 1fr;
  }
  
  .backup-controls {
    flex-direction: column;
    width: 100%;
  }
  
  .backup-btn, .export-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .format-card {
    flex-direction: column;
    text-align: center;
  }
  
  .format-features {
    justify-content: center;
  }
}