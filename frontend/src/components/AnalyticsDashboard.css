.analytics-container {
  padding: 0;
  max-width: none;
  margin: 0;
  background: white;
}

/* Health Banner */
.health-banner {
  background: #f9fafb;
  border-radius: 0;
  padding: 24px;
  margin: 0;
  box-shadow: none;
  border-left: 4px solid #10b981;
  border-bottom: 1px solid #e5e7eb;
}

.health-banner.health-poor {
  border-left-color: #ef4444;
}

.health-banner.health-fair {
  border-left-color: #f97316;
}

.health-banner.health-good {
  border-left-color: #f59e0b;
}

.health-banner.health-excellent {
  border-left-color: #10b981;
}

.health-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.health-score {
  flex-shrink: 0;
}

.health-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.health-circle::before {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
}

.health-score-text {
  font-size: 20px;
  font-weight: bold;
  color: #374151;
  z-index: 1;
}

.health-info {
  flex: 1;
}

.health-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #111827;
}

.health-info p {
  margin: 0 0 12px 0;
  color: #6b7280;
}

.health-issues {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.issue-tag {
  background: #fee2e2;
  color: #dc2626;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.health-actions {
  flex-shrink: 0;
}

.refresh-health-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.refresh-health-btn:hover {
  background: #2563eb;
}

/* Summary Cards */
.analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 24px 24px 32px 24px;
}

.summary-card {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: transform 0.2s, box-shadow 0.2s;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.summary-value {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-trend {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #374151;
  font-weight: 500;
}

.summary-progress {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.summary-indicator {
  font-size: 18px;
}

/* Analytics Sections */
.analytics-section {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
  margin: 0 24px 24px 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.analytics-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Source Breakdown */
.source-breakdown {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.source-item {
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.source-name {
  font-weight: 600;
  color: #374151;
}

.source-count {
  font-size: 14px;
  color: #6b7280;
}

.source-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.source-progress {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.source-issues {
  background: #3b82f6;
}

.source-webmethods {
  background: #8b5cf6;
}

.source-extractions {
  background: #10b981;
}

.source-other {
  background: #f59e0b;
}

/* Detailed Records */
.detailed-records {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detailed-record-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.record-rank {
  background: #3b82f6;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.record-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-id {
  font-weight: 600;
  color: #374151;
}

.record-length {
  font-size: 12px;
  color: #6b7280;
}

.record-bar {
  width: 100px;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.record-progress {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Quick Stats */
.quick-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin: 0 24px 24px 24px;
}

.stat-item {
  background: #f8fafc;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-1px);
}

.stat-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.stat-details {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Footer */
.analytics-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: 0 24px 24px 24px;
  background: #f8fafc;
  border-radius: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.last-updated {
  color: #6b7280;
  font-size: 14px;
}

.refresh-analytics-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.refresh-analytics-btn:hover {
  background: #059669;
}

/* Loading and Error States */
.analytics-loading, .analytics-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  margin-bottom: 20px;
  color: #dc2626;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
}

.retry-button:hover {
  background: #2563eb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-container {
    padding: 16px;
  }
  
  .analytics-summary {
    grid-template-columns: 1fr;
  }
  
  .health-content {
    flex-direction: column;
    text-align: center;
  }
  
  .analytics-footer {
    flex-direction: column;
    gap: 16px;
  }
}