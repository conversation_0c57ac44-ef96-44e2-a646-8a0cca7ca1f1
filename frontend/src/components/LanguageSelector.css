.language-selector {
  position: relative;
  display: inline-block;
  z-index: 2000;
}

.language-button {
  background: #ffffff;
  color: #374151;
  border: 2px solid #e5e7eb;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: clamp(14px, 2vw, 15px);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  min-width: 120px;
  justify-content: space-between;
}

.language-button:hover {
  border-color: #3b82f6;
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 1px 3px rgba(0, 0, 0, 0.1);
  color: #3b82f6;
}

.language-button:active {
  transform: translateY(1px);
}

.arrow {
  transition: transform 0.3s ease;
  font-size: 10px;
  color: #6b7280;
}

.arrow.open {
  transform: rotate(180deg);
}

.language-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.12),
    0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;
  min-width: 160px;
  z-index: 2001;
  animation: fadeInScale 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-option {
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: transparent;
  text-align: left;
  cursor: pointer;
  font-size: clamp(14px, 2vw, 15px);
  color: #374151;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  position: relative;
}

.language-option:hover {
  background: #f3f4f6;
  color: #3b82f6;
}

.language-option.active {
  background: #eff6ff;
  color: #3b82f6;
  font-weight: 600;
}

.language-option.active::after {
  content: '✓';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  color: #3b82f6;
  font-weight: 600;
}

.language-option:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.language-option:last-child {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .language-button {
    padding: 10px 16px;
    min-width: 120px;
  }
  
  .language-dropdown {
    min-width: 160px;
  }
  
  .language-option {
    padding: 14px 18px;
  }
}

@media (max-width: 576px) {
  .language-button {
    padding: 8px 14px;
    min-width: 100px;
  }
  
  .language-dropdown {
    min-width: 140px;
  }
  
  .language-option {
    padding: 12px 16px;
  }
}