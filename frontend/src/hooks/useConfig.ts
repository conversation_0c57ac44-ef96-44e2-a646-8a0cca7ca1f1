import { useState, useEffect } from 'react';
import { getConfig } from '../utils/config';

interface AppConfig {
  dashboardApiUrl: string;
  formSubmitUrl: string;
}

export const useConfig = () => {
  const [config, setConfig] = useState<AppConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);
        const appConfig = await getConfig();
        setConfig(appConfig);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load configuration');
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, []);

  return { config, loading, error };
};