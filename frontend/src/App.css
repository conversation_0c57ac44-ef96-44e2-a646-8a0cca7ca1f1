* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Import elegant fonts with Japanese support */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@200;300;400;500;600;700&display=swap');

.App {
  min-height: 100vh;
  position: relative;
  font-family: 'Inter', 'Noto Sans JP', 'Yu Gothic UI', 'Yu Gothic', 'Hiragino Kaku Gothic ProN', 
    'Hiragino Sans', 'Meiryo', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'liga' 1, 'calt' 1;
  background: #ffffff;
  color: #374151;
  letter-spacing: -0.005em;
  font-weight: 400;
}

/* Clean professional background with subtle pattern */
.App::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.03) 0%, transparent 50%),
    linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  z-index: -1;
}

/* Subtle geometric pattern overlay */
.App::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(90deg, rgba(148, 163, 184, 0.05) 1px, transparent 1px),
    linear-gradient(rgba(148, 163, 184, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: -1;
  opacity: 0.3;
}

.App-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 16px 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  transition: all 0.3s ease;
  flex-direction: column;
  gap: 16px;
}

.header-top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-bottom {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.company-branding {
  display: flex;
  align-items: center;
}

.company-name {
  font-size: clamp(20px, 3vw, 26px);
  font-weight: 500;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.3px;
  line-height: 1.1;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.tab-button {
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 400;
  font-size: 15px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  letter-spacing: -0.01em;
  min-width: 120px;
  text-align: center;
}

.tab-button:hover {
  color: #374151;
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

.tab-button.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 
    0 4px 16px rgba(59, 130, 246, 0.25),
    0 2px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  font-weight: 500;
}

.tab-button.active:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  color: white;
}

.App-main {
  padding: 140px 20px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
}

/* Responsive breakpoints */
@media (max-width: 1440px) {
  .App-main {
    padding: 100px 40px 40px;
  }
}

@media (max-width: 1200px) {
  .App-main {
    padding: 90px 30px 30px;
  }
}

@media (max-width: 992px) {
  .App-header {
    padding: 14px 24px;
    gap: 12px;
  }
  
  .tab-button {
    padding: 10px 18px;
    font-size: 14px;
    min-width: 100px;
  }
  
  .App-main {
    padding: 120px 20px 20px;
  }
}

@media (max-width: 768px) {
  .App-header {
    padding: 12px 20px;
    gap: 14px;
  }
  
  .company-name {
    font-size: 18px;
  }
  
  .tab-navigation {
    justify-content: center;
    padding: 4px;
  }
  
  .tab-button {
    padding: 10px 16px;
    font-size: 13px;
    min-width: 90px;
  }
  
  .App-main {
    padding: 120px 16px 20px;
  }
}

@media (max-width: 576px) {
  .App-header {
    padding: 10px 16px;
    gap: 12px;
  }
  
  .company-name {
    font-size: 16px;
  }
  
  .tab-button {
    padding: 8px 12px;
    font-size: 12px;
    min-width: 80px;
  }
  
  .App-main {
    padding: 110px 12px 16px;
  }
}

@media (max-width: 480px) {
  .App-header {
    padding: 10px 12px;
  }
  
  .company-name {
    font-size: 15px;
    letter-spacing: -0.3px;
  }
}

/* Hide scrollbar but keep functionality */
.App::-webkit-scrollbar {
  display: none;
}

.App {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Settings Page Styles */
.settings-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 100px;
  right: 32px;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toast {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  animation: slideInToast 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  font-weight: 500;
  font-size: 14px;
  min-width: 320px;
  max-width: 400px;
}

.toast-success {
  background: rgba(16, 185, 129, 0.95);
  color: white;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.toast-error {
  background: rgba(239, 68, 68, 0.95);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.toast-info {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.toast button {
  background: none;
  border: none;
  color: inherit;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin-left: 16px;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.toast button:hover {
  opacity: 1;
}

@keyframes slideInToast {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .toast-container {
    right: 16px;
    left: 16px;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
  
  .settings-page {
    padding: 16px;
  }
}
