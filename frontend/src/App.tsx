import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import './App.css';
import IssueForm from './components/IssueForm';
import EnhancedDashboard from './components/EnhancedDashboard';
import Settings from './components/Settings';
import LanguageSelector from './components/LanguageSelector';
import './i18n';

type TabType = 'issues' | 'dashboard' | 'settings';

interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'info';
  message: string;
}

function App() {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<TabType>('issues');
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  // Add toast notification
  const addToast = useCallback((type: ToastMessage['type'], message: string) => {
    const id = Math.random().toString(36).substring(7);
    setToasts(prev => [...prev, { id, type, message }]);
    setTimeout(() => {
      setToasts(prev => prev.filter(toast => toast.id !== id));
    }, 5000);
  }, []);

  return (
    <div className="App">
      <header className="App-header">
        <div className="header-top">
          <div className="company-branding">
            <h1 className="company-name">Kansai Electric</h1>
          </div>
          <LanguageSelector />
        </div>
        <div className="header-bottom">
          <div className="tab-navigation">
            <button
              className={`tab-button ${activeTab === 'issues' ? 'active' : ''}`}
              onClick={() => setActiveTab('issues')}
            >
              {t('issueReport')}
            </button>
            <button
              className={`tab-button ${activeTab === 'dashboard' ? 'active' : ''}`}
              onClick={() => setActiveTab('dashboard')}
            >
              {t('dashboard')}
            </button>
            <button
              className={`tab-button ${activeTab === 'settings' ? 'active' : ''}`}
              onClick={() => setActiveTab('settings')}
            >
              ⚙️ Settings
            </button>
          </div>
        </div>
      </header>
      <main className="App-main">
        {activeTab === 'issues' && <IssueForm />}
        {activeTab === 'dashboard' && <EnhancedDashboard />}
        {activeTab === 'settings' && (
          <div className="settings-page">
            <Settings
              onClose={() => setActiveTab('issues')}
              onSuccess={addToast.bind(null, 'success')}
              onError={addToast.bind(null, 'error')}
              isModal={false}
            />
          </div>
        )}
      </main>

      {/* Toast Notifications */}
      <div className="toast-container">
        {toasts.map(toast => (
          <div key={toast.id} className={`toast toast-${toast.type}`}>
            <span>{toast.message}</span>
            <button onClick={() => setToasts(prev => prev.filter(t => t.id !== toast.id))}>
              ×
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default App;
