interface AppConfig {
  dashboardApiUrl: string;
  formSubmitUrl: string;
}

let cachedConfig: AppConfig | null = null;

export const getConfig = async (): Promise<AppConfig> => {
  if (cachedConfig) {
    return cachedConfig;
  }

  // First priority: Check localStorage for user settings
  try {
    const savedSettings = localStorage.getItem('kansai-settings');
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      if (parsedSettings.dashboardApiUrl && parsedSettings.formSubmitUrl) {
        cachedConfig = {
          dashboardApiUrl: parsedSettings.dashboardApiUrl,
          formSubmitUrl: parsedSettings.formSubmitUrl
        };
        console.log('Using settings from localStorage:', cachedConfig);
        return cachedConfig;
      }
    }
  } catch (error) {
    console.warn('Failed to parse localStorage settings:', error);
  }

  // Second priority: Try to fetch config from backend
  try {
    const response = await fetch('/api/config');
    if (response.ok) {
      const config = await response.json();
      
      // Handle "self" value for dashboard API URL
      let dashboardApiUrl = config.dashboardApiUrl;
      if (dashboardApiUrl === "self") {
        // Use the current origin (works in both local and deployed environments)
        dashboardApiUrl = window.location.origin;
      }
      
      cachedConfig = {
        dashboardApiUrl: dashboardApiUrl,
        formSubmitUrl: config.formSubmitUrl
      };
      console.log('Using config from backend:', cachedConfig);
      return cachedConfig;
    }
  } catch (error) {
    console.warn('Failed to fetch config from backend, using fallback');
  }

  // Last priority: Fallback to environment variables or defaults
  cachedConfig = {
    dashboardApiUrl: process.env.REACT_APP_DASHBOARD_API_URL || window.location.origin,
    formSubmitUrl: process.env.REACT_APP_FORM_SUBMIT_URL || 'https://env769838.int-aws-us.webmethods.io/runflow/run/1UWXeQANOg'
  };
  
  console.log('Using fallback config:', cachedConfig);
  return cachedConfig;
};

// Reset cache function for testing
export const resetConfigCache = () => {
  cachedConfig = null;
};