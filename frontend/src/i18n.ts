import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

const resources = {
  en: {
    translation: {
      // Issue Form
      title: "Issue Report Form",
      date: "Date",
      issue: "Issue",
      description: "Description",
      submit: "Submit",
      language: "Language",
      english: "English",
      japanese: "Japanese",
      datePlaceholder: "Select date",
      issuePlaceholder: "Enter issue title",
      descriptionPlaceholder: "Describe the issue in detail",
      submitting: "Submitting...",
      success: "Form submitted successfully!",
      error: "Failed to submit form. Please try again.",
      
      // Navigation
      issueReport: "Issue Report",
      dashboard: "Dashboard",
      
      // Dashboard
      dashboardTitle: "Data Management Dashboard",
      records: "records",
      refresh: "Refresh",
      refreshing: "Refreshing...",
      loading: "Loading dashboard...",
      noData: "No data available",
      retry: "Retry",
      
      // Table Headers
      id: "ID",
      queries: "Queries",
      boxLink: "Box Link",
      actions: "Actions",
      createdAt: "Created",
      lastModified: "Modified",
      
      // Actions
      edit: "Edit",
      delete: "Delete",
      copy: "Copy",
      export: "Export",
      search: "Search",
      filter: "Filter",
      sort: "Sort",
      selectAll: "Select All",
      bulkDelete: "Bulk Delete",
      
      // Links
      viewFile: "View File",
      noLink: "No link",
      copyLink: "Copy Link",
      
      // Search & Filter
      searchPlaceholder: "Search queries, IDs, or links...",
      filterByDate: "Filter by Date",
      clearFilters: "Clear Filters",
      showingResults: "Showing {{count}} of {{total}} results",
      
      // Dialogs
      editRecord: "Edit Record #{{id}}",
      confirmDelete: "Confirm Delete",
      confirmBulkDelete: "Confirm Bulk Delete",
      authRequired: "Authentication Required",
      enterPassword: "Enter password to confirm:",
      confirmAction: "Are you sure you want to {{action}} record #{{id}}?",
      confirmBulkAction: "Are you sure you want to delete {{count}} selected records?",
      cancel: "Cancel",
      save: "Save Changes",
      confirmEdit: "Confirm Edit",
      confirmDeleteAction: "Confirm Delete",
      processing: "Processing...",
      
      // Form Fields
      queriesLabel: "Queries:",
      boxLinkLabel: "Box Link:",
      password: "Password",
      
      // Messages
      recordUpdated: "Record updated successfully",
      recordDeleted: "Record deleted successfully",
      recordsDeleted: "{{count}} records deleted successfully",
      linkCopied: "Link copied to clipboard",
      exportCompleted: "Data exported successfully",
      authFailed: "Authentication failed",
      invalidPassword: "Invalid password",
      recordNotFound: "Record not found",
      
      // Statistics
      totalRecords: "Total Records",
      withLinks: "With Box Links",
      withoutLinks: "Without Links",
      lastUpdate: "Last Updated",
      
      // Export
      exportToExcel: "Export to Excel",
      exportToCsv: "Export to CSV",
      exportSelected: "Export Selected",
      exportAll: "Export All",
      
      // Time
      justNow: "Just now",
      minutesAgo: "{{count}} minutes ago",
      hoursAgo: "{{count}} hours ago",
      daysAgo: "{{count}} days ago",
      
      // Pagination
      page: "Page",
      of: "of",
      itemsPerPage: "Items per page",
      showing: "Showing",
      to: "to",
      
      // Errors
      loadError: "Failed to load dashboard data",
      updateError: "Failed to update record",
      deleteError: "Failed to delete record",
      exportError: "Failed to export data",
      networkError: "Network connection error",
      serverError: "Server error occurred",
      
      // Additional
      selected: "selected",
      noDataToExport: "No data to export",
      dashboardRefreshed: "Dashboard refreshed"
    }
  },
  ja: {
    translation: {
      // Issue Form
      title: "問題報告フォーム",
      date: "日付",
      issue: "問題",
      description: "説明",
      submit: "送信",
      language: "言語",
      english: "英語",
      japanese: "日本語",
      datePlaceholder: "日付を選択",
      issuePlaceholder: "問題のタイトルを入力",
      descriptionPlaceholder: "問題を詳しく説明してください",
      submitting: "送信中...",
      success: "フォームが正常に送信されました！",
      error: "フォームの送信に失敗しました。もう一度お試しください。",
      
      // Navigation
      issueReport: "問題報告",
      dashboard: "ダッシュボード",
      
      // Dashboard
      dashboardTitle: "データ管理ダッシュボード",
      records: "件",
      refresh: "更新",
      refreshing: "更新中...",
      loading: "ダッシュボードを読み込み中...",
      noData: "データがありません",
      retry: "再試行",
      
      // Table Headers
      id: "ID",
      queries: "クエリ",
      boxLink: "Boxリンク",
      actions: "操作",
      createdAt: "作成日",
      lastModified: "更新日",
      
      // Actions
      edit: "編集",
      delete: "削除",
      copy: "コピー",
      export: "エクスポート",
      search: "検索",
      filter: "フィルター",
      sort: "並び替え",
      selectAll: "すべて選択",
      bulkDelete: "一括削除",
      
      // Links
      viewFile: "ファイルを表示",
      noLink: "リンクなし",
      copyLink: "リンクをコピー",
      
      // Search & Filter
      searchPlaceholder: "クエリ、ID、またはリンクを検索...",
      filterByDate: "日付でフィルター",
      clearFilters: "フィルターをクリア",
      showingResults: "{{total}}件中{{count}}件を表示",
      
      // Dialogs
      editRecord: "レコード #{{id}} を編集",
      confirmDelete: "削除の確認",
      confirmBulkDelete: "一括削除の確認",
      authRequired: "認証が必要です",
      enterPassword: "確認のためパスワードを入力してください：",
      confirmAction: "レコード #{{id}} を{{action}}してもよろしいですか？",
      confirmBulkAction: "選択された{{count}}件のレコードを削除してもよろしいですか？",
      cancel: "キャンセル",
      save: "変更を保存",
      confirmEdit: "編集を確認",
      confirmDeleteAction: "削除を確認",
      processing: "処理中...",
      
      // Form Fields
      queriesLabel: "クエリ：",
      boxLinkLabel: "Boxリンク：",
      password: "パスワード",
      
      // Messages
      recordUpdated: "レコードが正常に更新されました",
      recordDeleted: "レコードが正常に削除されました",
      recordsDeleted: "{{count}}件のレコードが正常に削除されました",
      linkCopied: "リンクがクリップボードにコピーされました",
      exportCompleted: "データのエクスポートが完了しました",
      authFailed: "認証に失敗しました",
      invalidPassword: "無効なパスワードです",
      recordNotFound: "レコードが見つかりません",
      
      // Statistics
      totalRecords: "総レコード数",
      withLinks: "Boxリンク有り",
      withoutLinks: "リンク無し",
      lastUpdate: "最終更新",
      
      // Export
      exportToExcel: "Excelにエクスポート",
      exportToCsv: "CSVにエクスポート",
      exportSelected: "選択項目をエクスポート",
      exportAll: "すべてをエクスポート",
      
      // Time
      justNow: "たった今",
      minutesAgo: "{{count}}分前",
      hoursAgo: "{{count}}時間前",
      daysAgo: "{{count}}日前",
      
      // Pagination
      page: "ページ",
      of: "/",
      itemsPerPage: "ページあたりの項目数",
      showing: "表示中",
      to: "〜",
      
      // Errors
      loadError: "ダッシュボードデータの読み込みに失敗しました",
      updateError: "レコードの更新に失敗しました",
      deleteError: "レコードの削除に失敗しました",
      exportError: "データのエクスポートに失敗しました",
      networkError: "ネットワーク接続エラー",
      serverError: "サーバーエラーが発生しました",
      
      // Additional
      selected: "選択済み",
      noDataToExport: "エクスポートするデータがありません",
      dashboardRefreshed: "ダッシュボードが更新されました"
    }
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en',
    fallbackLng: 'en',
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage']
    },
    interpolation: {
      escapeValue: false
    }
  });

export default i18n;