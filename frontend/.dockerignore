# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
yarn.lock

# Production build
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
.nyc_output/

# Misc
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Docker
Dockerfile
.dockerignore
docker-compose.yml

# Git
.git/
.gitignore

# Logs
logs
*.log