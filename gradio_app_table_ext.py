"""
Gradio web interface for the requirements extraction pipeline.
"""

import gradio as gr
import json
import tempfile
import os
from pathlib import Path
import logging
from datetime import datetime
import traceback

# Load environment variables first
from dotenv import load_dotenv
load_dotenv(override=True)

from backend.utils.config import Config, WatsonXConfig
from backend.services.pipeline import RequirementsExtractionPipeline
from backend.utils.extraction_utils import setup_logging

# Configure logging
setup_logging("INFO")
logger = logging.getLogger(__name__)


class RequirementsExtractionApp:
    """Gradio app for requirements extraction"""
    
    def __init__(self):
        self.pipeline = None
        self.current_config = None
        
    def create_pipeline(self, api_key, project_id, ibm_cloud_url, model_id, temperature, max_tokens):
        """Create pipeline with given configuration"""
        try:
            if not api_key or not project_id:
                return False, "❌ API Key and Project ID are required"
            
            if not ibm_cloud_url:
                ibm_cloud_url = "https://us-south.ml.cloud.ibm.com"  # Default URL
            
            # Create WatsonX configuration
            watsonx_config = WatsonXConfig(
                api_key=api_key.strip(),
                project_id=project_id.strip(),
                ibm_cloud_url=ibm_cloud_url.strip(),
                model_id=model_id,
                temperature=temperature,
                max_tokens=int(max_tokens)
            )
            
            # Create main configuration
            config = Config(watsonx=watsonx_config)
            
            # Initialize pipeline
            self.pipeline = RequirementsExtractionPipeline(config)
            self.current_config = config
            
            return True, "✅ Pipeline initialized successfully"
            
        except Exception as e:
            logger.error(f"Failed to create pipeline: {str(e)}")
            return False, f"❌ Pipeline creation failed: {str(e)}"
    
    def test_connection(self, api_key, project_id, ibm_cloud_url, model_id, temperature, max_tokens):
        """Test connection to WatsonX"""
        try:
            success, message = self.create_pipeline(api_key, project_id, ibm_cloud_url, model_id, temperature, max_tokens)
            
            if not success:
                return message
            
            # Test connection
            if self.pipeline.test_connection():
                return "✅ WatsonX connection successful!"
            else:
                return "❌ WatsonX connection failed. Check your credentials."
                
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return f"❌ Connection test error: {str(e)}"
    
    def load_env_credentials(self):
        """Load credentials from environment variables"""
        try:
            # Reload environment variables
            load_dotenv(override=True)
            
            api_key = os.getenv('WATSONX_API_KEY', '')
            project_id = os.getenv('WATSONX_PROJECT_ID', '')
            ibm_cloud_url = os.getenv('IBM_CLOUD_URL', 'https://us-south.ml.cloud.ibm.com')
            model_id = os.getenv('WATSONX_MODEL_ID', 'meta-llama/llama-3-3-70b-instruct')
            temperature = float(os.getenv('WATSONX_TEMPERATURE', '0.3'))
            max_tokens = int(os.getenv('WATSONX_MAX_TOKENS', '8192'))
            
            if api_key and project_id:
                status_msg = "✅ Credentials loaded from environment!"
            else:
                status_msg = "⚠️ Some credentials missing in environment"
            
            return (
                api_key,
                project_id, 
                ibm_cloud_url,
                model_id,
                temperature,
                max_tokens,
                status_msg
            )
            
        except Exception as e:
            logger.error(f"Failed to load environment credentials: {str(e)}")
            return (
                "",
                "",
                "https://us-south.ml.cloud.ibm.com",
                "meta-llama/llama-3-3-70b-instruct",
                0.3,
                8192,
                f"❌ Error loading environment: {str(e)}"
            )
    
    def extract_requirements(
        self, 
        pdf_file,
        api_key, 
        project_id,
        ibm_cloud_url, 
        model_id, 
        temperature, 
        max_tokens,
        table_confidence_threshold,
        enable_llm_fallback,
        enable_validation,
        progress=gr.Progress()
    ):
        """Extract requirements from uploaded PDF"""
        try:
            # Update progress
            progress(0.1, desc="Initializing pipeline...")
            
            # Validate inputs
            if pdf_file is None:
                return None, "❌ Please upload a PDF file", "", ""
            
            if not api_key or not project_id:
                return None, "❌ Please provide WatsonX API Key and Project ID", "", ""
            
            # Create pipeline with current settings
            progress(0.2, desc="Setting up pipeline...")
            success, message = self.create_pipeline(api_key, project_id, ibm_cloud_url, model_id, temperature, max_tokens)
            
            if not success:
                return None, message, "", ""
            
            # Update extraction configuration
            self.current_config.extraction.table_confidence_threshold = table_confidence_threshold
            self.current_config.extraction.llm_fallback_enabled = enable_llm_fallback
            self.current_config.extraction.enable_validation = enable_validation
            
            # Re-create pipeline with updated config
            self.pipeline = RequirementsExtractionPipeline(self.current_config)
            
            # Get PDF file path
            pdf_path = pdf_file.name
            
            progress(0.3, desc="Extracting requirements...")
            
            # Extract requirements
            result = self.pipeline.extract_requirements(pdf_path)
            
            progress(0.8, desc="Validating results...")
            
            # Validate results
            if enable_validation:
                result = self.pipeline.validate_extraction_result(result)
            
            progress(0.9, desc="Preparing output...")
            
            # Create output JSON
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"requirements_extraction_{timestamp}.json"
            
            # Export to temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json_output = self.pipeline.export_to_json(result)
                f.write(json_output)
                output_path = f.name
            
            # Prepare summary
            summary = self.create_summary(result)
            
            # Prepare detailed results for display
            detailed_results = self.format_detailed_results(result)
            
            progress(1.0, desc="Extraction completed!")
            
            return output_path, summary, detailed_results, json_output
            
        except Exception as e:
            logger.error(f"Extraction failed: {str(e)}")
            error_msg = f"❌ Extraction failed: {str(e)}\n\nFull traceback:\n{traceback.format_exc()}"
            return None, error_msg, "", ""
    
    def create_summary(self, result):
        """Create a summary of extraction results"""
        try:
            if not result.success:
                return f"""## ❌ Extraction Failed

**Error:** {result.error_message}

**Method Attempted:** {result.method_used.value}
**Confidence Score:** {result.confidence_score:.3f}
"""
            
            data = result.data
            req_count = data.get('total_requirements', 0) or data.get('total_count', 0)
            
            summary = f"""## ✅ Extraction Successful!

**Method Used:** {result.method_used.value}
**Confidence Score:** {result.confidence_score:.3f}
**Requirements Found:** {req_count}

### Summary Statistics:
"""
            
            # Add summary stats if available
            if 'summary' in data:
                summary_stats = data['summary']
                summary += f"- **Total Requirements:** {summary_stats.get('total_count', req_count)}\n"
                
                if 'by_page' in summary_stats:
                    summary += f"- **Pages with Requirements:** {list(summary_stats['by_page'].keys())}\n"
                
                if 'by_method' in summary_stats:
                    methods = summary_stats['by_method']
                    summary += f"- **Extraction Methods:** {', '.join(methods.keys())}\n"
                
                if 'average_confidence' in summary_stats:
                    avg_conf = summary_stats['average_confidence']
                    summary += f"- **Average Confidence:** {avg_conf:.3f}\n"
            
            # Add processing time if available
            if 'processing_time_seconds' in data:
                proc_time = data['processing_time_seconds']
                summary += f"- **Processing Time:** {proc_time:.2f} seconds\n"
            
            # Add validation info if available
            if 'validation' in data:
                validation = data['validation']
                if validation.get('validation_passed'):
                    valid_count = validation.get('valid_requirement_count', req_count)
                    original_count = validation.get('original_requirement_count', req_count)
                    summary += f"- **Validation:** ✅ Passed ({valid_count}/{original_count} valid)\n"
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to create summary: {str(e)}")
            return f"❌ Failed to create summary: {str(e)}"
    
    def format_detailed_results(self, result):
        """Format detailed results for display"""
        try:
            if not result.success:
                return "No detailed results available due to extraction failure."
            
            data = result.data
            requirements = data.get('requirements', [])
            
            if not requirements:
                return "No requirements found in the document."
            
            detailed = "## 📋 Detailed Requirements\n\n"
            
            for i, req in enumerate(requirements, 1):
                detailed += f"### {i}. Requirement\n"
                detailed += f"**Content:** {req.get('requirement', 'N/A')}\n\n"
                
                if req.get('witness'):
                    detailed += f"**Witness:** {req.get('witness')}\n\n"
                
                if req.get('item'):
                    detailed += f"**Item:** {req.get('item')}\n\n"
                
                if req.get('details'):
                    detailed += f"**Details:** {req.get('details')}\n\n"
                
                # Add metadata
                metadata = []
                if req.get('source_page'):
                    metadata.append(f"Page {req.get('source_page')}")
                if req.get('extraction_method'):
                    metadata.append(f"Method: {req.get('extraction_method')}")
                if req.get('confidence'):
                    metadata.append(f"Confidence: {req.get('confidence'):.3f}")
                
                if metadata:
                    detailed += f"*{' | '.join(metadata)}*\n\n"
                
                detailed += "---\n\n"
            
            return detailed
            
        except Exception as e:
            logger.error(f"Failed to format detailed results: {str(e)}")
            return f"❌ Failed to format detailed results: {str(e)}"

# Initialize the app
app = RequirementsExtractionApp()

# Create Gradio interface
def create_interface():
    """Create the Gradio interface"""
    
    # Check if environment variables are loaded
    env_check_msg = "💡 Click 'Load from .env' to load credentials from your .env file"
    if os.getenv('WATSONX_API_KEY') and os.getenv('WATSONX_PROJECT_ID'):
        env_check_msg = "✅ Credentials found in environment! You can use them or enter new ones below."
    elif os.path.exists('.env'):
        env_check_msg = "📁 .env file found! Click 'Load from .env' to load your credentials."
    else:
        env_check_msg = "⚠️ No .env file found. Please enter credentials manually or create a .env file."
    
    with gr.Blocks(
        title="Requirements Extraction Pipeline",
        theme=gr.themes.Soft(),
        css=""".gradio-container {max-width: 1200px !important}"""
    ) as interface:
        
        gr.Markdown("""
        # 📄 Requirements Extraction Pipeline
        
        Extract "要求事項" (requirements) from Japanese construction specification documents using AI-powered table detection and LLM analysis.
        
        **Features:**
        - 🔍 Multi-method table detection (Camelot, Tabula, PDFPlumber)
        - 🤖 WatsonX LLM fallback for complex documents
        - 📊 Confidence scoring and validation
        - 📁 Structured JSON output with metadata
        """)
        
        with gr.Tab("📄 Extract Requirements"):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("### 📁 Upload Document")
                    pdf_input = gr.File(
                        label="PDF Document",
                        file_types=[".pdf"],
                        type="filepath"
                    )
                    
                    gr.Markdown("### 🤖 WatsonX Configuration")
                    
                    with gr.Group():
                        with gr.Row():
                            api_key_input = gr.Textbox(
                                label="API Key",
                                placeholder="Enter your WatsonX API key",
                                type="password",
                                value=os.getenv('WATSONX_API_KEY', ''),
                                scale=3
                            )
                            
                            load_env_btn = gr.Button(
                                "🔄 Load from .env", 
                                variant="secondary",
                                scale=1,
                                size="sm"
                            )
                        
                        project_id_input = gr.Textbox(
                            label="Project ID", 
                            placeholder="Enter your WatsonX project ID",
                            value=os.getenv('WATSONX_PROJECT_ID', '')
                        )
                        
                        ibm_cloud_url_input = gr.Textbox(
                            label="IBM Cloud URL",
                            placeholder="Enter IBM Cloud URL (optional)",
                            value=os.getenv('IBM_CLOUD_URL', 'https://us-south.ml.cloud.ibm.com')
                        )
                        
                        model_dropdown = gr.Dropdown(
                            label="Model",
                            choices=[
                                "meta-llama/llama-3-3-70b-instruct",
                                "meta-llama/llama-3-70b-instruct",
                                "meta-llama/llama-3-8b-instruct",
                                "ibm/granite-13b-chat-v2",
                                "ibm/granite-7b-lab"
                            ],
                            value=os.getenv('WATSONX_MODEL_ID', 'meta-llama/llama-3-3-70b-instruct')
                        )
                        
                        with gr.Row():
                            temperature_slider = gr.Slider(
                                label="Temperature",
                                minimum=0.0,
                                maximum=1.0,
                                value=float(os.getenv('WATSONX_TEMPERATURE', '0.3')),
                                step=0.1
                            )
                            
                            max_tokens_slider = gr.Slider(
                                label="Max Tokens",
                                minimum=1000,
                                maximum=8192,
                                value=int(os.getenv('WATSONX_MAX_TOKENS', '8192')),
                                step=1000
                            )
                    
                    test_connection_btn = gr.Button("🔧 Test Connection", variant="secondary")
                    connection_status = gr.Textbox(label="Connection Status", interactive=False)
                    
                    # Environment status
                    env_status = gr.Textbox(
                        label="Environment Status", 
                        interactive=False,
                        value=env_check_msg
                    )
                    
                    gr.Markdown("### ⚙️ Extraction Settings")
                    
                    with gr.Group():
                        table_confidence_threshold = gr.Slider(
                            label="Table Confidence Threshold",
                            minimum=0.0,
                            maximum=1.0,
                            value=0.6,
                            step=0.1,
                            info="Minimum confidence for table extraction before LLM fallback"
                        )
                        
                        enable_llm_fallback = gr.Checkbox(
                            label="Enable LLM Fallback",
                            value=True,
                            info="Use LLM extraction if table detection fails"
                        )
                        
                        enable_validation = gr.Checkbox(
                            label="Enable Validation",
                            value=True,
                            info="Validate and clean extraction results"
                        )
                    
                    extract_btn = gr.Button("🚀 Extract Requirements", variant="primary", size="lg")
                
                with gr.Column(scale=2):
                    gr.Markdown("### 📊 Results")
                    
                    summary_output = gr.Markdown(label="Summary")
                    
                    with gr.Tabs():
                        with gr.Tab("📋 Detailed View"):
                            detailed_output = gr.Markdown()
                        
                        with gr.Tab("📄 Raw JSON"):
                            json_output = gr.Code(
                                language="json",
                                label="Raw JSON Output"
                            )
                    
                    download_output = gr.File(
                        label="📥 Download Results (JSON)",
                        visible=True
                    )
        
        with gr.Tab("ℹ️ Help & Examples"):
            gr.Markdown("""
            ## 📖 How to Use
            
            ### 0. Setup .env File (Recommended)
            Create a `.env` file in your project directory with:
            ```
            WATSONX_API_KEY=your_api_key_here
            WATSONX_PROJECT_ID=your_project_id_here
            IBM_CLOUD_URL=https://us-south.ml.cloud.ibm.com
            ```
            Then click "🔄 Load from .env" to automatically fill the form!
            
            ### 1. Upload PDF Document
            - Upload a Japanese construction specification document (PDF format)
            - Supported formats: Text-based PDFs (not scanned images)
            
            ### 2. Configure WatsonX
            - **API Key**: Your IBM WatsonX API key
            - **Project ID**: Your WatsonX project identifier
            - **IBM Cloud URL**: Your IBM Cloud instance URL (defaults to us-south)
            - **Model**: Choose the LLM model (Llama-3.3-70B recommended for best results)
            - **Temperature**: Controls randomness (0.3 = balanced, 0.1 = deterministic, 1.0 = creative)
            - **Max Tokens**: Maximum response length from the model (up to 8192)
            
            ### 3. Adjust Settings
            - **Table Confidence Threshold**: Higher values = stricter table detection
            - **LLM Fallback**: Enable to use AI when table detection fails
            - **Validation**: Enable to clean and validate results
            
            ### 4. Extract & Download
            - Click "Extract Requirements" to start processing
            - View results in the Summary and Detailed tabs
            - Download the complete JSON file for further use
            
            ## 🔧 Extraction Pipeline
            
            The system uses a dual-route approach:
            
            1. **Table Detection**: First tries to detect and extract requirements from tables using:
               - Camelot (high-precision table detection)
               - Tabula (robust table extraction)
               - PDFPlumber (text-based table detection)
            
            2. **LLM Fallback**: If table detection fails or confidence is low:
               - Uses WatsonX LLM with specialized Japanese prompts
               - Extracts requirements from unstructured text
               - Provides confidence scoring
            
            ## 📋 Output Format
            
            Results include:
            - **requirement**: The actual requirement text
            - **witness**: Whether witness/standby is required (○/×)
            - **item**: Item numbers or classifications
            - **details**: Additional context or details
            - **source_page**: Page number where found
            - **confidence**: Reliability score (0.0-1.0)
            
            ## 🚨 Troubleshooting
            
            ### Common Issues:
            - **Connection Failed**: Check API key and project ID
            - **No Tables Found**: Document may have poor table formatting
            - **Low Confidence**: Try adjusting threshold or enable LLM fallback
            - **No Requirements**: Document may not contain standard requirement sections
            
            ### Tips for Better Results:
            - Use high-quality, text-based PDFs
            - Ensure clear table formatting in source documents
            - Use lower temperature (0.1) for more consistent results
            - Enable validation for cleaner output
            """)
        
        with gr.Tab("🔧 Settings"):
            gr.Markdown("""
            ## Environment Variables
            
            You can set these environment variables to avoid entering credentials each time:
            
            ```bash
            export WATSONX_API_KEY="your_api_key_here"
            export WATSONX_PROJECT_ID="your_project_id_here"
            ```
            
            ## Advanced Configuration
            
            For programmatic use, you can import and use the pipeline directly:
            
            ```python
            from requirements_extraction import Config, RequirementsExtractionPipeline
            
            config = Config.from_env()
            pipeline = RequirementsExtractionPipeline(config)
            result = pipeline.extract_requirements("document.pdf")
            ```
            """)
        
        # Event handlers
        load_env_btn.click(
            fn=app.load_env_credentials,
            inputs=[],
            outputs=[
                api_key_input,
                project_id_input,
                ibm_cloud_url_input,
                model_dropdown,
                temperature_slider,
                max_tokens_slider,
                env_status
            ]
        )
        
        test_connection_btn.click(
            fn=app.test_connection,
            inputs=[
                api_key_input,
                project_id_input,
                ibm_cloud_url_input, 
                model_dropdown,
                temperature_slider,
                max_tokens_slider
            ],
            outputs=connection_status
        )
        
        extract_btn.click(
            fn=app.extract_requirements,
            inputs=[
                pdf_input,
                api_key_input,
                project_id_input,
                ibm_cloud_url_input,
                model_dropdown,
                temperature_slider,
                max_tokens_slider,
                table_confidence_threshold,
                enable_llm_fallback,
                enable_validation
            ],
            outputs=[
                download_output,
                summary_output,
                detailed_output,
                json_output
            ]
        )
    
    return interface

# Create and launch the interface
if __name__ == "__main__":
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )