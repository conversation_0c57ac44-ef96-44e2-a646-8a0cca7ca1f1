version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - BOX_CLIENT_ID=${BOX_CLIENT_ID}
      - BOX_CLIENT_SECRET=${BOX_CLIENT_SECRET}
      - BOX_ACCESS_TOKEN=${BOX_ACCESS_TOKEN}
      - BOX_REFRESH_TOKEN=${BOX_REFRESH_TOKEN}
      - WATSONX_API_KEY=${WATSONX_API_KEY}
      - WATSONX_PROJECT_ID=${WATSONX_PROJECT_ID}
      - UPLOAD_FOLDER=/app/uploads
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/UploadedData:/app/UploadedData
      - ./backend/ExcelData:/app/ExcelData
      - ./backend/PdfData:/app/PdfData
      - uploads:/app/uploads
    networks:
      - kansai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - kansai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  uploads:

networks:
  kansai-network:
    driver: bridge